
## Online Step:
1. 在所有k3s服务器,修改/etc/hosts, 增加 192.168.0.X self.host
2. git clone rmprodcfg
3. cd rmprodcfg/k3s/goauth
4. cp /home/<USER>/rmprodcfg/k3s /opt/kube/
5. 按照下面的操作

```
kubectl delete configmap goauth-config

kubectl create secret docker-registry ghcr-creds-rain \
  --docker-server=ghcr.io \
  --docker-username=rainlaurent \
  --docker-password=YOUR_PERSONAL_ACCESS_TOKEN

kubectl create secret generic mongo-certs \
  --from-file=server.pem=/etc/mongo/server.pem \
  --from-file=ca.crt=/etc/mongo/ca.crt

kubectl get secret

# Create configmap from configuration file
kubectl create configmap goauth-config --from-file=./container.ini

# Verify configmap creation
kubectl get configmap goauth-config -o yaml

kubectl rollout restart deployment/goauth-server

kubectl apply -f goauthK3s.yaml


kubectl logs pod/goauth-server-5758c88f79-pfrrr
kubectl exec -it pod/goauth-server-59d498c8bf-7s7x2 -- /bin/sh 


kubectl exec -it net-debug -- bash
kubectl get pod -o wide

curl -k http://*************:30099/info/healthz
```


## 如果不设置hostNetwork， ip只有2个
bash-5.1# ip a
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
    inet 127.0.0.1/8 scope host lo
       valid_lft forever preferred_lft forever
    inet6 ::1/128 scope host
       valid_lft forever preferred_lft forever
2: eth0@if9: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1450 qdisc noqueue state UP group default qlen 1000
    link/ether ce:ed:8d:0d:68:50 brd ff:ff:ff:ff:ff:ff link-netnsid 0
    inet *********/24 brd *********** scope global eth0
       valid_lft forever preferred_lft forever
    inet6 fe80::cced:8dff:fe0d:6850/64 scope link
       valid_lft forever preferred_lft forever