[dbs]
verbose = 3
[dbs.tmp]
uri = "mongodb://dbRur:<EMAIL>:27018/listing?authSource=admin&replicaSet=prod&tls=true&tlsCAFile=/etc/mongo/ca.crt&tlsCertificateKeyFile=/etc/mongo/server.pem&tlsAllowInvalidCertificates=true&readPreference=nearest&minPoolSize=1&maxPoolSize=100"
[dbs.data]
uri = "mongodb://dbRur:<EMAIL>:27018/data?authSource=admin&replicaSet=prod&tls=true&tlsCAFile=/etc/mongo/ca.crt&tlsCertificateKeyFile=/etc/mongo/server.pem&tlsAllowInvalidCertificates=true&readPreference=nearest&minPoolSize=1&maxPoolSize=100"
[dbs.listing]
uri = "mongodb://dbRur:<EMAIL>:27018/listing?authSource=admin&replicaSet=prod&tls=true&tlsCAFile=/etc/mongo/ca.crt&tlsCertificateKeyFile=/etc/mongo/server.pem&tlsAllowInvalidCertificates=true&readPreference=nearest&minPoolSize=1&maxPoolSize=100"


[golog]
dir = "/app/logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "json"
standard_output = true

[auth]
jwtSecret = "b9lzajnaW8KpRq2YeMzTn7LQvHXaBmZ4gCt3uJ5F29lzajna"

[app]
version = "6.6.3"