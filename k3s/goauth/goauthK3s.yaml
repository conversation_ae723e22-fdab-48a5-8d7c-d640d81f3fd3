apiVersion: apps/v1
kind: Deployment
metadata:
  name: goauth-server
spec:
  # 1. 增加副本数到 3，提高可用性
  replicas: 3
  # 2. 配置滚动更新策略，实现零停机
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1  # 更新过程中，最多有1个Pod不可用
      maxSurge: 1        # 更新过程中，最多比期望副本数多1个Pod
  selector:
    matchLabels:
      app: goauth-server
  template:
    metadata:
      labels:
        app: goauth-server
    spec:
      hostNetwork: true # 否则访问不到其他网卡里的机器
      # 3. 配置Pod反亲和性，将Pod分散到不同节点
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - goauth-server
              topologyKey: "kubernetes.io/hostname"
      imagePullSecrets:
        - name: ghcr-creds-rain
      containers:
        - name: goauth-server
          image: ghcr.io/real-rm/goauth-server:v0.0.8
          ports:
            - containerPort: 8099
          # 4. 同时配置 livenessProbe 和 readinessProbe
          livenessProbe:
            httpGet:
              path: /info/healthz
              port: 8099
            initialDelaySeconds: 30 # 适当延长启动延迟
            periodSeconds: 20
            timeoutSeconds: 5 # 5秒超时
            failureThreshold: 3 # 允许3次失败再重启
          readinessProbe:
            httpGet:
              path: /info/healthz # 通常可以和 livenessProbe 使用相同的端点
              port: 8099
            initialDelaySeconds: 15
            periodSeconds: 10
          env:
            - name: RMBASE_FILE_CFG
              value: /app/configs/container.ini
          volumeMounts:
            - name: config-volume
              mountPath: /app/configs/container.ini
              subPath: container.ini
            - name: mongo-cert-volume
              mountPath: /etc/mongo # The directory inside the container
              readOnly: true
      volumes:
        - name: config-volume
          configMap:
            name: goauth-config
        - name: mongo-cert-volume
          secret:
            secretName: mongo-certs
            defaultMode: 0444 # 默认给了root user
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        runAsNonRoot: true

---

apiVersion: v1
kind: Service
metadata:
  name: goauth-service
spec:
  type: NodePort
  selector:
    app: goauth-server
  ports:
    - protocol: TCP
      port: 8099
      targetPort: 8099
      nodePort: 30099  # 访问方式：<node-ip>:31999

