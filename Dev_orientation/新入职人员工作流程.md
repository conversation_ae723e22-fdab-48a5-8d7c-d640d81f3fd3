# 工作流程:
- 记录日常完成内容,包括coding，开会/交流, 每天更新到钉钉Lark/DingTalk(日报)
- 上传修改到bitbucket
- 每天工作内容记录同步到timesheet


# 工作内容：
- @fred/PM 安排的内容
- 如果代码不运行或有任何疑问马上问*，不要尝试自己解决
- 工作完成后发起Pull Request
- PR Reviewer review 完成后群里通知PM
- 如果有接下来的安排就顺序执行，没有的话问@fred/PM


# 开会时间： 见Lark 日历
国内GMT+8：<1.5hr
- 周一 9pm-10pm(+-0.5hr) @fred
- 周四 9pm-10pm(+-0.5hr)

# 会议内容
1. 说明上周工作内容及进度
   1. 如果有需要展示或者需要分享的内容，提前打开
2. 提出问题(问题过于复杂等会议结束，单独提问)
3. 说明后续工作
