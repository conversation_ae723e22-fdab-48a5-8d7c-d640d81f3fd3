# 安装环境
  - nvm 使用nvm 安装node， node(20)
  - 安装mongo mongo(6.0)
  - redis
  - python
  - elasticsearch
  - xcode+Android studio

# 申请账号
  - bitbucket (代码管理，找负责人要邀请)
  - ssh key (测试服的需要指定id id_ed25519)
  - realmaster app(项目本身)
  - zoom (例会)
  - Lark 飞书 （例会、报告）
  - apple Notes 同步账号

# 常用APP
  - robo 3t/studio 3t (mongo可视化管理)
  - outline (vpn)
    ss://Y2hhY2hhMjAtaWV0Zi1wb2x5MTMwNTpQQTQ2NUFsakFTcHFNbDN4QjRmSU5u@74.48.162.155:19555/?outline=1
  - browser（any with debugger）
  - zoom
  - visual studio code
  - iterm2+zsh+omzsh
  - Lark 飞书（找负责人要邀请）

# 设置mongo
  mongo需要创建账户，信息需要对应在cfglocal/base.yml mongodb
  详见：/realmaster-appweb/docs/Dev_enviroment_setup(init)/01_mongodb.md
  1. mongo启动后连接本地mongo(mongosh),在命令行输入 rs.initiate()
  2. 生成keyfile
    openssl rand -base64 741 > mongodb-keyfile
    chmod 600 mongodb-keyfile
  3. mongo配置参考 mongod.conf文件，注意对应path
  4. 创建mongo用户，权限root
    use admin
    db.createUser({user:'d1',pwd:'d1',roles:[{role:'root',db:'admin'}]})
    db.auth('d1','d1')

# 启动项目设置
  1. 电脑设置 etc/host 添加  127.0.0.1       localhost www.test app.test
  2. 建议使用vs code编辑器，使用vcode_setting.json
  3. 项目clone之后需要在cfglocal、realmaster-appweb/src、realmaster-appweb3/src/webDev路径下下载node modules (npm i)
  4. 修改cfglocal/base.yml
  5. 启动项目时需要启动redis和mongo后，才能成功启动项目

# 测试环境设置
  1. 复制cfglocal -> cfglocalTest
  2. 修改cfglocal/base.yml, cfglocal/tpls/config4.coffee
  3. Mongodb 需要创建新collection 和 testUser
  4. ./test.sh -f libapp/geoCodeHelper.js

# vs code插件
  1. coffeescript preview
  2. coffee lint
  3. draw.io
  4. markdown
  5. rest client
  6. ai
