# 说明
如果有新需求或者修复需要遵循以下步骤完成开发

# 开发步骤
- 从release branch checkout -b [new_branch]
- 在/realmaster-appweb/docs/Change_requirements/增加文件说明，遵循文件结构
- 文件说明和解决办法部分完成后和需求提出人进行讨论
- 需求确认无误后进行开发
- 开发中段需要和PM讨论具体代买实现细节和数据结构
- 增加Unit Test Cases，需要修改realmaster-appweb/docs/Dev_unitTest/tests/passedFiles.csv or passedModules.csv
- 修改完成和UT passed以后提交PR进行peer review



# Review步骤
- 按照/realmaster-appweb/docs/Change_requirements/下的说明文件测试功能的实现
- 需要遵循realmaster-appweb/docs/Dev_standard/reviewSteps.md 分3步去review修改
- review完成后Approve PR并且在群里通知PM


# 上线步骤
- 重新测试功能，测试后端是否出错
- UT测试需要pass
- 合并到release branch
- 重新测试UT
- release branch PR 到 Production branch
- 所有服务器提交修改