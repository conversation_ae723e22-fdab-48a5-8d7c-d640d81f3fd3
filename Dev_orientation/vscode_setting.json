{"atlascode.jira.workingSite": {"baseUrlSuffix": "atlassian.net"}, "editor.suggestSelection": "first", "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue", "python.jediEnabled": false, "terminal.integrated.shell.osx": "/bin/zsh", "terminal.integrated.inheritEnv": false, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.tabSize": 2, "coffeelinter.defaultRules": {"arrow_spacing": {"level": "ignore"}, "braces_spacing": {"level": "ignore", "spaces": 0, "empty_object_spaces": 0}, "camel_case_classes": {"level": "error"}, "coffeescript_error": {"level": "error"}, "colon_assignment_spacing": {"level": "ignore", "spacing": {"left": 0, "right": 0}}, "cyclomatic_complexity": {"level": "ignore", "value": 10}, "duplicate_key": {"level": "error"}, "empty_constructor_needs_parens": {"level": "ignore"}, "ensure_comprehensions": {"level": "warn"}, "eol_last": {"level": "ignore"}, "indentation": {"value": 2, "level": "error"}, "line_endings": {"level": "ignore", "value": "unix"}, "max_line_length": {"value": 100, "level": "warn", "limitComments": false}, "missing_fat_arrows": {"level": "ignore", "is_strict": false}, "newlines_after_classes": {"value": 3, "level": "ignore"}, "no_backticks": {"level": "error"}, "no_debugger": {"level": "warn", "console": false}, "no_empty_functions": {"level": "ignore"}, "no_empty_param_list": {"level": "ignore"}, "no_implicit_braces": {"level": "ignore", "strict": true}, "no_implicit_parens": {"level": "ignore", "strict": true}, "no_interpolation_in_single_quotes": {"level": "ignore"}, "no_nested_string_interpolation": {"level": "warn"}, "no_plusplus": {"level": "ignore"}, "no_private_function_fat_arrows": {"level": "warn"}, "no_stand_alone_at": {"level": "ignore"}, "no_tabs": {"level": "error"}, "no_this": {"level": "warn"}, "no_throwing_strings": {"level": "error"}, "no_trailing_semicolons": {"level": "error"}, "no_trailing_whitespace": {"level": "error", "allowed_in_comments": false, "allowed_in_empty_lines": true}, "no_unnecessary_double_quotes": {"level": "warn"}, "no_unnecessary_fat_arrows": {"level": "warn"}, "non_empty_constructor_needs_parens": {"level": "warn"}, "prefer_english_operator": {"level": "warn", "doubleNotLevel": "warn"}, "space_operators": {"level": "ignore"}, "spacing_after_comma": {"level": "ignore"}, "transform_messes_up_line_numbers": {"level": "warn"}}}