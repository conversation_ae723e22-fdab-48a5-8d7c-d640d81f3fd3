# README #

This repo only contains .local config and production keys for new config
记录RealMaster开发相关的说明文件。

### What is this repository for? ###
* 相关开发规范和细节见realmaster-appweb/docs/
* 新功能需要把说明文档加到realmaster-appweb/docs/Change_requirements
* 开发相关的比如数据结构，API仍然在realmaster-appweb/docs/
* document only, no need to npm install
* 0.0.1

### How do I get set up? ###

* copy and paste local config to https://bitbucket.org/fredxiang/rmconfig/src/master/
* run start.sh

### Contribution guidelines ###

* !!! plase update files in this repo and then pull on server
* !!! do not modify on server directly

### Who do I talk to? ###

* Repo owner or admin
* Other community or team contact


## TODO: 
need to sort .local.ini fields