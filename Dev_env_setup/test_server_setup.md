## add test users

set up first user of appd1

useradd -b /opt -G elasticsearch -k /opt/appd1 -m -s /bin/bash appd2

vim /etc/logrotate.d/appd1

```
/opt/appd1/cfglocal/logs/*log {
    daily
    copytruncate
    rotate 2
    missingok
    notifempty
    compress
    delaycompress
    sharedscripts
    postrotate
    endscript
    su appd1 appd1
}
```

add logrotate for appd2-appd9

```
for d in 2 3 4 5 6 7 8 9 ; do cp appd1 "appd$d" ; done
for d in 2 3 4 5 6 7 8 9 ; do sed -i "s/appd1/appd$d/g" "appd$d" ; done
```

vim /etc/nginx/d1_rm.conf

```
upstream appd1 {
    server      127.0.0.1:8081  max_fails=10    fail_timeout=60s;
    keepalive   10;
}
server {
    listen 80;
    server_name  d1.realmaster.com d1w.realmaster.com dd1.realmaster.com dd1w.realmaster.com;

    charset utf-8;

    location ~* ^/(web|js|css|img|fonts|musics|libs|wecardBgs|html|favicon|views) {
        root    /opt/appd1/realmaster-appweb/src/webroot/public;
        expires -1;
        #limit_req zone=one burst=50;
        #limit_conn addr 20;
    }

    location ~* ^/(schimgs) {
        root    /var/www/node/rm/imgs;
        expires -1;
        #limit_req zone=one burst=1;
        #limit_conn addr 1;
    }

    location / {
        proxy_pass      http://appd1;
        proxy_connect_timeout   5s;
        proxy_http_version      1.1;
        proxy_set_header        Host    $host;
        proxy_set_header        X-Real-IP       $remote_addr;
        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header        X-Forwarded-Proto $scheme;

        # support socket.io
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        expires -1;
    }

    error_page  404              /error.html;
    error_page 500 502 503 504 /error.html;
    location = /error.html {
        root    /opt/appd1/realmaster-appweb/src/webroot/public/errorPage;
        internal;
        #limit_req zone=one burst=1;
        #limit_conn addr 1;
    }

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    location ~ /\.ht {
        deny  all;
    }
    client_body_timeout 5s;
    client_header_timeout 5s;

    listen 443 ssl http2; # managed by Certbot
    ssl_certificate /root/.acme.sh/realmaster.com/fullchain.cer; # managed by Certbot
    ssl_certificate_key /root/.acme.sh/realmaster.com/realmaster.com.key; # managed by Certbot
    include /etc/nginx/default.d/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/nginx/default.d/ssl-dhparams.pem; # managed by Certbot
}

server {
    listen 80;
    server_name  realmaster.cn www.realmaster.cn d1.realmaster.cn d1w.realmaster.cn dd1.realmaster.cn dd1w.realmaster.cn;
    # d1.realmaster.cn d1w.realmaster.cn d1.realmaster.cn dd1w.realmaster.cn;

    charset utf-8;

    location ~* ^/(web|js|css|img|fonts|musics|libs|wecardBgs|html|favicon|views) {
        root    /opt/appd1/realmaster-appweb/src/webroot/public;
        expires -1;
        #limit_req zone=one burst=50;
        #limit_conn addr 20;
    }

    location ~* ^/(schimgs) {
        root    /var/www/node/rm/imgs;
        expires -1;
        #limit_req zone=one burst=1;
        #limit_conn addr 1;
    }

    location / {
        proxy_pass      http://appd1;
        proxy_connect_timeout   5s;
        proxy_http_version      1.1;
        proxy_set_header        Host    $host;
        proxy_set_header        X-Real-IP       $remote_addr;
        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header        X-Forwarded-Proto $scheme;

        # support socket.io
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        expires -1;
    }

    error_page  404              /error.html;
    error_page 500 502 503 504 /error.html;
    location = /error.html {
        root    /opt/appd1/realmaster-appweb/src/webroot/public/errorPage;
        internal;
        #limit_req zone=one burst=1;
        #limit_conn addr 1;
    }

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    location ~ /\.ht {
        deny  all;
    }
    client_body_timeout 5s;
    client_header_timeout 5s;

    listen 443 ssl http2; # managed by Certbot
    ssl_certificate /root/.acme.sh/realmaster.cn/fullchain.cer; # managed by Certbot
    ssl_certificate_key /root/.acme.sh/realmaster.cn/realmaster.cn.key; # managed by Certbot
    include /etc/nginx/default.d/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/nginx/default.d/ssl-dhparams.pem; # managed by Certbot

}
```

```
for d in $(seq 2 9); do rm -f d${d}_rm.conf; done
for d in 2 3 4 5 6 7 8 9 ; do cp d1_rm.conf "d${d}_rm.conf" ; done
for d in 2 3 4 5 6 7 8 9 ; do sed -i "s/8081/808$d/g" "d${d}_rm.conf" ; done
for d in 2 3 4 5 6 7 8 9 ; do sed -i "s/d1/d$d/g" "d${d}_rm.conf" ; done
for d in $(seq 2 9); do usermod -a -G appd$d nginx; done
nginx -t
systemctl restart nginx
```
