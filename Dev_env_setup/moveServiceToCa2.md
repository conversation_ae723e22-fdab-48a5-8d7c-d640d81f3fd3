# activate ca2 as main server involves the following tasks

## as root

- copy ca folder contents
- copy bin folder contents
- update crontab on ca2 and ca1
- unmount ca1 folders

## wordpress

- file sync to ca3
  - nohup /home/<USER>/bin/syncRmWp.sh > /home/<USER>/logs/syncRmWp.log 2>&1 &
  - kill syncRmWp / inotifywait on ca2
- db as main: stop sync from ca2
  - STOP ALL SLAVES
  - SHOW SLAVE STATUS \G

## on main user

### file upload/download

- Need to try upload/download using local /etc/hosts first.

### sync dl.realmaster.cn/com

- kill syncRmDl on ca2
- bin/syncRmDl.sh > logs/syncRmDl.log

### cmate

- start/check es
- start cmate
- check app/web/wordpress using local /etc/hosts

### change dns on .com/.cn

### change batch and crontab

- stop all crontab on ca1
- start all crontab on ca2
- stop all screen-ed jobs on ca1
- start all screen-ed jobs on ca2

* nohup ./start.sh lib/batchBase.coffee batch/prop/condosSqft.coffee force >> ./logs/condoSqft.log 2>&1 &
  - Error: There is no leader for this topic-partition as we are in the middle of a leadership election
