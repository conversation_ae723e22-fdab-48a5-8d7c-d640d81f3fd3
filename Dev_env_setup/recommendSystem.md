# Recommend System Setup

## Clone the repository

```shell
<NAME_EMAIL>:fredxiang/rmrecsys.git
cd rmrecsys
git checkout production
git pull
npm install
```

move the folder to /opt/

```shell
mv /home/<USER>/rmrecsys /opt/
```

## add user

as root

```shell
mkdir /opt/epipe
useradd -m -d /opt/epipe epipe
cd /opt/epipe
mkdir logs
vim /opt/epipe/event-pipe-production.toml
vim /opt/epipe/.bashrc
chown -R epipe:epipe /opt/epipe
# chown -R epipe:epipe /opt/rmrecsys
/bin/su - epipe
```

## as epipe

cat /opt/epipe/event-pipe-production.toml

```toml
[service]
header_ip_key = 'x-real-ip'
user_post_keys = ["client_ts", "obj", "sub_obj", "act", "uuid", "obj_id", "user_roles", "tgp", "query_string",
    "platform", "src", "client_ip"]
user_post_keys_required = ["obj", "act", "uuid", "obj_id", "user_roles", "platform"]
user_post_keys_fillauto = ["client_ts", "client_ip"]
system_add_keys = ["server_name", "host_name","log_ts"]
headers_to_save = ['ua','x-geocountry','x-georegion']
log_dir = '/opt/epipe/logs/'
stats_wait_time_ms = 60000
stats_print_header_interval = 60
health_check_wait_time_ms = 5000

[input]
[input.http]
enable = 1
ip = "127.0.0.1"
port = 3000

[input.kafka]
enable = 1
servers = ["ca1:9192", "ca2:9192", "ca3:9192"]
group_id = "user-behavour-logger"
topic = "China.event.user-to-doris"

[output]
[output.kafka]
enable = 0
servers = ["localhost:19092", "localhost:29092", "localhost:39092"]
topic = "user-events-to-doris"
sleep_time_ms = 2500

[output.doris]
enable = 1
servers = ["ca4:29030", "ca5:29030", "ca11:29030"]
user = "user"
password = "password"
database = "realmaster"
table = "logger"
batch_save_num = 100
sleep_time_ms = 1000
connection_limit = 10
queue_limit = 10
restore_node_timeout_ms = 3000
```

/opt/epipe/.bashrc

```shell
export EVENT_PIPE_CONNECTOR_CONFIG_FILE=/opt/epipe/event-pipe-production.toml
```

## as epipe user

```shell
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
source ~/.nvm/nvm.sh
nvm install v20
npm install pm2 -g
source ~/.bashrc
echo $EVENT_PIPE_CONNECTOR_CONFIG_FILE
../rmrecsys/event-pipe-connector/bin/start_event_pipe_connector
```

cat .bashrc

```shell
export EVENT_PIPE_CONNECTOR_CONFIG_FILE=/opt/epipe/event-pipe-production.toml

export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
```

## systemd start/stop

vim /etc/systemd/system/epipe.service

```ini
[Unit]
Description=Event Pipe Connector
After=NetworkManager.target

[Service]
#Type=forking
Type=simple
User=epipe
Group=epipe
#CPUSchedulingPriority=2
#IOSchedulingClass=idle
#IOSchedulingPriority=7
#BlockIOWeight=100
#ExecStart=/bin/sh -c '/opt/doris/be/bin/start_be.sh'
ExecStart=/bin/sh -c 'source /opt/epipe/.bashrc; node /opt/rmrecsys/event-pipe-connector/src/event-pipe-connector.js > /opt/epipe/logs/epipe.log 2>&1'
#ExecStop=/opt/doris/be/bin/stop_be.sh
Restart=on-abnormal
TimeoutStopSec=5
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```shell
systemctl daemon-reload
systemctl enable --now epipe
```

## logrotate

vim /etc/logrotate.d/epipe

```config
/opt/epipe/logs/\*log {
    daily
    copytruncate
    rotate 10
    missingok
    notifempty
    compress
    sharedscripts
    postrotate
    endscript
    su epipe epipe
}
```
