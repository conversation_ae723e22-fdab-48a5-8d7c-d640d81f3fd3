#!/bin/bash
#this file setup appweb and config after test server setup and rocky9 setup
npm install --global coffeescript

sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"

set -e
zsh
#assume using zsh+wd
omz plugin enable wd
wd list
cd ~ && mkdir bin


# install nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.2/install.sh | bash
# add nvm to zsh
echo '
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
' >> ~/.zshrc

source ~/.zshrc

# install node stable
nvm install stable
nvm ls

# clone repo, must ssh -A to server
cd ~
<NAME_EMAIL>:real-rm/appweb.git appweb
<NAME_EMAIL>:real-rm/rmconfig.git rmconfig

cd rmconfig && wd add cfg && npm i && cd ..
cd appweb/src && wd add src && npm i && cd ..
cd ..
#cp config.ini and config.test.ini

#install mongo not using root
# ln -s  ./mongodb-linux-x86_64-rhel93-8.0.4/bin/* ~/bin

#install elastic
wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-8.17.0-linux-x86_64.tar.gz
wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-8.17.0-linux-x86_64.tar.gz.sha512
shasum -a 512 -c elasticsearch-8.17.0-linux-x86_64.tar.gz.sha512
tar -xzf elasticsearch-8.17.0-linux-x86_64.tar.gz
cd elasticsearch-8.17.0/
export ES_HOME=`pwd`


# 接下来操作需要手动配置
# vi ./config/jvm.options
# uncomment -Xms4g
# -Xmx4g
# vi ./config/elasticsearch.yml

# cluster.name: cab1tt #my-application
# node.name: node-cab1tt
# http.port: 9201
# cluster.initial_master_nodes: ["node-cab1tt"]

# install redis need root
yum install redis
systemctl enable redis
systemctl start redis


#./bin/elasticsearch
# paste password to ~/elasticPass.md 
export ELASTIC_PASSWORD="_i8WflNSgKjyPaooJlom"
# ctrl +d
# ./bin/elasticsearch -d
cd ..
echo "curl --cacert $ES_HOME/config/certs/http_ca.crt -u elastic:$ELASTIC_PASSWORD https://localhost:9201" > ./testEs.sh
chmod +x testEs.sh
./testEs.sh

# install mongosh
cd ~
# wget https://downloads.mongodb.com/compass/mongodb-mongosh-2.3.7.x86_64.rpm
# test mongo
use admin
db.createUser(
  {
    user: "root",
    pwd: "rootForCab1Test",
    roles: [ { role: "userAdminAnyDatabase", db: "admin" }, "readWriteAnyDatabase" ]
  }
)
db.createUser(
    {
    user: "admin",
    pwd: "rootForCab1Test",
    roles: [ 
    { 
    role: "readWriteAnyDatabase", 
    db: "admin" 
    }, 
    {
    "role" : "dbAdminAnyDatabase",
    "db" : "admin"
    },
    {
    "role" : "clusterAdmin",
    "db" : "admin"
    },
    "userAdminAnyDatabase" 
    ]
    }
)
# 参考docs/Dev_enviroment_setup(init)/01_mongodb.md
#测试连接
mongosh --tlsCAFile /etc/mongo/ca.crt --host cab1 --tls --authenticationDatabase admin -u d1 -p d1 --port 28017
use listingTest
db.prov_city_cmty.insertOne(
  {
    _id: {
      prov: 'Ontario',
      city: 'Smiths Falls',
      cmty: 'Montague/Marlboro Forest'
    },
    lat: 45.038328,
    lng: -75.********,
    latMin: 45.0374527,
    lngMin: -75.9168407,
    latMax: 45.0418292,
    lngMax: -75.9116426,
    c: 5
  })

# copy local.test.ini to wd cfg
./start.sh -t unit_test -e <EMAIL>
./start.sh -t unit_testES -e <EMAIL>

./unitTest/compile.sh -s /home/<USER>/appweb/src
./unitTest/test.sh -f lib/helpers_date.js
./unitTest/test.sh -m propertyImport

touch  ./keys/key.pem
touch  ./keys/cert.pem
echo "{}">./keys/service-account.json

echo '127.0.0.1 app.test www.test' >> /etc/hosts

cfg = rs.conf()
# do not use ip
cfg.members[0].host = "cab1:28017" 
rs.reconfig(cfg)