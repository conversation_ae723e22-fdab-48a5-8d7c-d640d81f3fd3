| floorplan          | ca1, ca3          | ca       | ca1(watch)->ca3(receive,classify)->ca3(+watermark,save to db)          |                  |

## floorplan
# 1 raid ssd/hdd config: proc/mdstate
phoSrc: /mnt/md0/mlsimgs/treb/mls
write: /mnt/md0/mls_floorplan -> nginx img.realmaster.com/mfp/[0-z]/dhai78xa78.jpg

## floorplan来源:
a. manuual
b. mls photo
c. next, pdf parsing

## floorplan file loc:

backup: /opt/rmfu/f_realmaster_cn/backup_floorplan 
watermark: /opt/rmfu/f_realmaster_cn/watermark_floorplan

pthon[pyfpimg]: ca3
coffee[floorPlanClassification]: ca3
coffee[watchPropAndSendFloorPlanClassification]: ca1

TODO: 
1 doc, rmcfg+appweb
2 ca3 配置(python+appweb+cfg), kafaka已配置，需要测试
3 ca1 watch send to kafka