https://github.com/karljohns0n/nginx-more

## install nginx-more

```
dnf install -y https://repo.aerisnetwork.com/pub/aeris-release-9.rpm
dnf install -y nginx-more

nginx -V
nginx version: nginx/1.24.0
custom build maintained on github.com/karljohns0n/nginx-more
built by gcc 11.3.1 20220421 (Red Hat 11.3.1-2) (GCC)
built with OpenSSL 3.1.0 14 Mar 2023
TLS SNI support enabled
configure arguments: --prefix=/usr/share/nginx --sbin-path=/usr/sbin/nginx --modules-path=/usr/lib64/nginx/modules --conf-path=/etc/nginx/nginx.conf --error-log-path=/var/log/nginx/error.log --http-log-path=/var/log/nginx/access.log --http-client-body-temp-path=/var/lib/nginx/cache/client_body --http-proxy-temp-path=/var/lib/nginx/cache/proxy --http-fastcgi-temp-path=/var/lib/nginx/cache/fastcgi --http-uwsgi-temp-path=/var/lib/nginx/cache/uwsgi --http-scgi-temp-path=/var/lib/nginx/cache/scgi --pid-path=/var/run/nginx.pid --lock-path=/var/run/nginx.lock --user=nginx --group=nginx --with-compat --with-file-aio --with-http_ssl_module --with-http_realip_module --with-http_addition_module --with-http_image_filter_module --with-http_sub_module --with-http_dav_module --with-http_flv_module --with-http_mp4_module --with-http_gunzip_module --with-http_gzip_static_module --with-http_random_index_module --with-http_secure_link_module --with-http_degradation_module --with-http_stub_status_module --with-http_auth_request_module --with-http_xslt_module --with-http_v2_module --with-mail --with-mail_ssl_module --with-threads --with-stream --with-stream_ssl_module --with-stream_realip_module --with-http_slice_module --with-stream_ssl_preread_module --with-debug --with-cc-opt='-O2 -flto=auto -ffat-lto-objects -fexceptions -g -grecord-gcc-switches -pipe -Wall -Werror=format-security -Wp,-D_FORTIFY_SOURCE=2 -Wp,-D_GLIBCXX_ASSERTIONS -specs=/usr/lib/rpm/redhat/redhat-hardened-cc1 -fstack-protector-strong -specs=/usr/lib/rpm/redhat/redhat-annobin-cc1 -m64 -march=x86-64-v2 -mtune=generic -fasynchronous-unwind-tables -fstack-clash-protection -fcf-protection -DTCP_FASTOPEN=23' --with-openssl=modules/openssl-3.1.0 --with-openssl-opt=enable-ktls --with-http_v2_hpack_enc --add-dynamic-module=modules/ngx_modsecurity-1.0.3 --add-module=modules/ngx_headers_more-0.34 --add-module=modules/ngx_cache_purge-2.3 --add-module=modules/ngx_brotli-1.0.0rc-2-g6e97 --add-module=modules/ngx_module_vts-0.2.1 --add-module=modules/ngx_http_geoip2_module-3.4 --add-module=modules/ngx_echo-0.63
```

### install GeoIP2 if needed

dnf install libmaxminddb python3-geoip2 -y

### vim /etc/nginx/nginx.conf

```
      log_format      main    '$remote_addr - $remote_user [$time_local] "$request" '
      '$status $body_bytes_sent "$http_referer" '
      '"$http_user_agent" "$http_x_forwarded_for" '
      '$request_time $upstream_response_time $pipe '
      '$geoip2_data_country_code $geoip2_region_name'; # data_city_name is not accurate

      # GeoIP2
      geoip2 /usr/share/GeoIP/GeoLite2-Country.mmdb {
          auto_reload 60m;
          $geoip2_metadata_country_build metadata build_epoch;
          $geoip2_data_country_code country iso_code;
          $geoip2_data_country_name country names en;
      }
      geoip2 /usr/share/GeoIP/GeoLite2-City.mmdb {
          auto_reload 60m;
          $geoip2_metadata_city_build metadata build_epoch;
          $geoip2_data_city_name city names en;
          $geoip2_data_time_zone location time_zone;
          $geoip2_latitude location latitude;
          $geoip2_longitude location longitude;
          $geoip2_postal_code postal code;
          $geoip2_region_name subdivisions 0 names en;
          $geoip2_region subdivisions 0 iso_code;
      }
```

comment out gzip_http_version, otherwise nginx cache bug will return some page with 0 length content.

```
#gzip_http_version 1.1;
```

setsebool -P httpd_read_user_content 1

https://stackoverflow.com/questions/27435655/proxy-pass-isnt-working-when-selinux-is-enabled-why

### wordpress

#### mariadb/mysql

```
dnf install -y mariadb mariadb-server
systemctl start --now mariadb
mysql_secure_installation
```

Switch to unix_socket authentication [Y/n] N

Change the root password? [Y/n] n

sudo mariadb

```
CREATE USER wpProdU@localhost IDENTIFIED BY 'GuYAg9cUFbgvfseM';
CREATE DATABASE rmWpProd;
GRANT ALL PRIVILEGES ON rmWpProd.* TO 'wpProdU'@'localhost';

CREATE USER wpBackUp@localhost IDENTIFIED BY 'GuYAg9cUFbgvfseM';
GRANT RELOAD ON *.* TO 'wpBackUp'@'localhost';
GRANT PROCESS ON *.* TO 'wpBackUp'@'localhost';
GRANT BINLOG MONITOR ON *.* TO 'wpBackUp'@'localhost';

```

#### restore wordpress database and files

gunzip 2023-07-02-wp.sql.gz
sudo mariadb -D rmWpProd < 2023-07-02-wp.sql

adduser wpusr
tar xzf 2023-07-02-wp.tgz
chown -R wpusr:wpusr /opt/rmwp

#### php-fpm

dnf install -y php-fpm php-mysqlnd php-gd php-xml php-mbstring php-json php-opcache php-pecl-apcu php-pecl-zip php-pecl-imagick
adduser wpusr
vim /etc/php-fpm.d/www.conf

```
user = wpusr
group = wpusr
listen = 127.0.0.1:9000
listen.owner = wpusr
```

vim /etc/php.ini

```
upload_max_filesize = 64M
post_max_size = 64M
```

systemctl start --now php-fpm

### set up nginx config

cp config files for /etc/nginx/conf.d/\*.conf, default.d/\*.conf and /etc/nginx/nginx.conf

default.d/options-ssl-nginx.conf and ssl-dhparams.pem

vim /etc/nginx/conf.d/\*.conf
%s/***************/**************/g

or

cd /etc/nginx/conf.d/
sed -i 's/***************/**************/g' \*.conf

nginx.conf
include /etc/nginx/conf.d/\*.conf;
comment out other include lines

#### ssl keys

adduser scphom
sudo -u scphom mkdir /home/<USER>/.ssh
sudo -u scphom chmod 700 /home/<USER>/.ssh
sudo -u scphom vim /home/<USER>/.ssh/authorized_keys

```
sh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIPugrcErqQTxeYbFFhIk+fIlYQdvWc57H4Vq6Q+J/Rdu root@ca3
```

sudo -u scphom chmod 600 /home/<USER>/.ssh/authorized_keys

### setup cmate

- install nvm
- install nodejs
- git clone /opt/rmnode, $HOME/rmcfg
- npm install -g coffeescript
- usermod -a -G elasticsearch ca1

check apps in tpls/config.coffee

#### /opt folders

rmnode/realmaster-appweb
rmfu/
rmdl/dl.realmaster.cn
rmwp/wordpress

## setup file sync

dnf install inotify-tools -y

```
while true; do
  inotifywait -r -e modify,create,delete,delete_self,move /opt/rmdl/
  rsync -avz --del /opt/rmdl/ /mnt/ca3rmdl/
done
```

nohup ~/bin/syncRmWp.sh > ~/logs/syncRmWp.log 2>&1 &

## setup mariadb replicaset

[docs](https://mariadb.com/kb/en/setting-up-replication/)
[master-master](https://computingforgeeks.com/mariadb-master-master-replication-on-ubuntu/)
[star](https://fromdual.com/replication-in-a-star) log_slave_updates = 0;
[multi-source](https://mariadb.com/kb/en/multi-source-replication/)

as root:
on primary:

vim /etc/my.cnf

```
[mariadb]
log-bin
server_id=2
log-basename=master2
binlog-format=mixed
```

systemctl restart mysqld
mariadb

```
select version(), @@server_id;
SELECT user,host FROM mysql. user;
CREATE USER 'repl'@'ca3' IDENTIFIED BY 'Tv92CNN4wQlR';
GRANT REPLICATION SLAVE ON *.* TO 'repl'@'ca3';
FLUSH PRIVILEGES;
FLUSH TABLES WITH READ LOCK;
SHOW MASTER STATUS;
```

make backup on primary:

```
mkdir /mnt/ssd0/dump
cd /mnt/ssd0/dump

mariabackup --backup --target-dir=./ --user=wpBackUp --password=GuYAg9cUFbgvfseM
mariabackup --prepare --target-dir=./

rsync -avP ./ /mnt/ca3rmfu/mariabk/
```

if on slave: --slave-info --safe-slave-backup
mariadb

```
UNLOCK TABLES;
```

on slave:

```
mv /var/lib/mysql /var/lib/mysql.bak
mkdir /var/lib/mysql
mariabackup --copy-back --target-dir=/mnt/ca3rmfu/mariabk
chown -R mysql:mysql /var/lib/mysql
```

vim /etc/my.cnf

```
[mariadb]
log-bin
server_id=3
log-basename=master3
binlog-format=mixed
```

systemctl enable --now mariadb

mariadb

```
CHANGE MASTER TO
  MASTER_HOST='ca2',
  MASTER_USER='repl',
  MASTER_PASSWORD='Tv92CNN4wQlR',
  MASTER_PORT=3306,
  MASTER_LOG_FILE='master2-bin.000004',
  MASTER_LOG_POS=531643325,
  MASTER_CONNECT_RETRY=10;
START SLAVE;
SHOW SLAVE STATUS \G
```

confirm the following:

```
Slave_IO_Running: Yes
Slave_SQL_Running: Yes
```

on slavie, to prevent wordpress from writing to slave db:

```
systemctl stop php-fpm
```

## setup HTTP3/H3

https://github.com/cloudflare/quiche/issues/236

### add this to conf.d/xxx.conf in server section

```ini
include conf.d/h3.part
```

### conf.d/h3.part

```ini
add_header Alt-Svc 'h3=":443"; ma=86400';
listen 443 ssl;
listen 443 quic; # reuseport;
http2 on;
http3 on;
quic_retry on;
ssl_early_data on;
quic_gso on;
ssl_protocols TLSv1.3;
```
