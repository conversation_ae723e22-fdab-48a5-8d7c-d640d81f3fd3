# Doris Configuration

## change setting as root

```shell
echo madvise | sudo tee /sys/kernel/mm/transparent_hugepage/enabled
adduser -d /opt/doris doris
chown -R doris:doris /opt/apache-doris-*
chown -R doris:doris /mnt/md0/doris*
```

## Doris systemd config

vim /etc/systemd/system/doris-be.service

```ini
[Unit]
Description=Doris BackEnd
After=NetworkManager.target

[Service]
#Type=forking
Type=simple
User=doris
Group=doris
#CPUSchedulingPriority=2
#IOSchedulingClass=idle
#IOSchedulingPriority=7
#BlockIOWeight=100
#ExecStart=/bin/sh -c '/opt/doris/be/bin/start_be.sh'
ExecStart=/opt/doris/be/bin/start_be.sh
ExecStop=/opt/doris/be/bin/stop_be.sh
Restart=on-abnormal
TimeoutStopSec=10
RestartSec=10

[Install]
WantedBy=multi-user.target
```

vim /etc/systemd/system/doris-fe.service

```ini
[Unit]
Description=Doris FrontEnd
After=NetworkManager.target

[Service]
Type=simple
User=doris
Group=doris
#CPUSchedulingPriority=2
#IOSchedulingClass=idle
#IOSchedulingPriority=7
#BlockIOWeight=100
#ExecStart=/bin/sh -c '/opt/doris/be/bin/start_be.sh'
ExecStart=/opt/doris/fe/bin/start_fe.sh
ExecStop=/opt/doris/fe/bin/stop_fe.sh
Restart=on-abnormal
TimeoutStopSec=10
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## start doris as services

```shell
systemctl daemon-reload
systemctl enable --now doris-be
systemctl enable --now doris-fe
```
