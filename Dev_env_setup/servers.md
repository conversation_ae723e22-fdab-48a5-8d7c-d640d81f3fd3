## Servers

| server | services                                                           | location | IPs                 | memo                  |
| ------ | ------------------------------------------------------------------ | -------- | ------------------- | --------------------- |
| ca1    | app/web/wp/fu/dl,mongo,es,batch,kafka,img-proxy,sslCA              | ca       | ************* \*1   | main server           |
| ca2    | app,web,wp,fu/dl hot stand by,mongo2,kafka2,sslCA2,images for treb | ca       | *********** \*1     | main server           |
| ca3    | images,rni,mongo2,kafka2,app/web/wp/fu/img hot stand by,sslCA2     | ca       | *************** \*1 | image & main backup   |
| ca5    | doris,epipe, ML-train                                              | ca       | ************* \*1   |                       |
| ap1    | app,web,mongo2,es,monitor                                          | asia     | ************* \*    | asia main             |
| shf3   | app,web,mongo2,es,monitor                                          | china    | ***************     | china main            |
| shf4   | mongo2,ss27098,haproxy                                             | china    | **************      | china mongo2          |
| vg20   | haproxy,ss27099,kafka-mm                                           | us       | *************       | proxy server          |
| ca4    | rni2,kafka-pub,cc-mail,3rdparty-web, ML-backup,doris,epipe         | ca       | *************       | kafka/rni/mail server |
| ca11   | rni2, ca4-backup, ML ,doris,epipe                                  | ca       | ***************     | kafka/rni/mail server |
| ca12   | test(appweb,mongo,es,kafka)                                        | ca       | *************       | test server main      |
| ca10   | images for crea                                                    | ca       | *************** \*1 | image-backup server   |

\*1 inter-server and ssh ip.

- all other IPs are mix-used for both interal and exteral services

## Services

| service           | server             | location | IPs/ports                                              | memo                    |
| ----------------- | ------------------ | -------- | ------------------------------------------------------ | ----------------------- |
| app/dl            | ca1 (bk:ca2,ca3)   | ca       | ***************, **************, *************         |                         |
| web/wp            | ca1 (bk:ca2,ca3)   | ca       | ***************, **************, *************         |                         |
| app/web/dl(China) | shf3               | ca       | ***************, **************, *************         | no wordpress            |
| app/web(Asia)\*   | ap1                | ca       | *************                                          | proxy wordpress. Unused |
| mongo             | ca1,2,3,ap1,shf3,4 | ca,china | 27011,27012,27013,27009,27003,27004                    |                         |
| es                | ca1,2,3,ap1,shf3   | ca,china |                                                        |                         |
| batch             | ca2 => ca1         | ca       |                                                        |                         |
| kafka             | shf3               | china    | PLAINTEXT:9092,SASL_SSL:9093,SASL_SSL(CONTROLLER):9094 | China                   |
| kafka             | ca1,ca2,ca3        | ca       | PLAINTEXT:9192,SASL_SSL:9193,SASL_SSL(CONTROLLER):9194 | Main                    |
| kafka             | ca4,ca11           | ca       | PLAINTEXT:9292,SASL_SSL:9293,SASL_SSL(CONTROLLER):9294 | Gateway                 |
| fu                | ca2                | ca       |                                                        |                         |
| ssl               | ca2                | ca       |                                                        |                         |
| images storage    | ca3, ca2, ca10     | ca       | *************, **************, 192.99.126.123          |                         |
| images nginx      | ca1                | ca       | proxy to ca3,ca2,ca10                                  |                         |
| rni               | ca3,4,11           | ca       | 27037                                                  |                         |
| kafka-mm          | vg20, ca1          | ca       | China -> vg20 -> Main, Main <-> ca1 <-> Gate           | /opt/kafka/config       |

## TODO:

- [x] ssl @ ca3
- [x] add kafka replication @ ca2, ca3, ca4，ca11: 9192,9193,9194
- [x] app/web backup @ ca3
- [x] update production; file upload/download @ ca2, with backup @ca3
- [x] wp backup @ ca3
- [x] setup test server @ ca12: es, mongo+ssl, kafka+ssl,
- [x] c12: add new users
- [x] new ca1
- [x] ca5 => ca12 reminings
- [x] restart rni with TLS
- [x] restart batches imports
- [x] main mongo primary to ca3
- [x] ca2 es/mongo => /mnt/ssd0/
- [x] restart main mongo with TLS: preferTLS now
- [x] restart all app/web servers: shf3, ap1, ca1, ca2, ca3
- [x] kafka Main, Public, China

- [ ] main server => ca1

  - [x] wordpress sync
    - [x] mariadb star-replicaton sync
    - [x] file sync from ca2
  - [x] file sync ca2 => ca3; ca2 => ca1
  - [x] upload file sync ca1 => ca2 => ca3
  - [x] main mongo => ca1
  - [ ] kafka change: batch and remote
  - [ ] main batch server to ca1

- [x] cc-mail @ nc1 to ca4 , ca11
- [x] move avion/4salebc from ca9 to ca4/ca11
- [x] kafka-pub @ ca9 to ca4, ca11
- [ ] setup db restore script @ ca3,ca2, ca1

- [x] ca2 for treb only image, ca10 for crea only image, ca1 as main proxy, ca3 as backup
- [x] setup image proxy @ca3, move from ca1,ca6 to ca3

- [x] update vg20
- [x] reformat ca10, test cache: with writeback, normal, without cache

- [x] remove ca6,ca1
- [x] remove ca5,ca7
- [x] remove ca9
- [x] remove nc1

- [ ] clean up main.nft, setup rkhunter, hosts, fail2ban, etc
- [ ] rmcfg documents update: rocky9
- [ ] update all keys: google, aws, etc

## kafka structure

- existig: ca2,ca3,ca4,ca11

  - SASAL_SSL : 9193
  - PLAIN: 9192

- Main: ca1,2,3

  - SASL_SSL: 9193
  - PLAIN: 9192

- China: shf3

  - PLAIN: 9192

- Public: ca4, ca11

  - SASL_SSL: 9293

- mm2 China-Ca: shf3 => ca1-3. Unidirection: event.\*
- mm2 Private-Public: ca1-3 <==> ca4,11 only certain topics: req.\* , resp.\*

## to promote ca3 to primary server

systemctl enable --now php-fpm

ca3m:

- start syncRmDl.sh
- check file upload to ca2rmfu
- check photo write back to ca10 and ca2
- check backup to cloud ftp

wpusr:

- start syncRmWp.sh

update all dns
