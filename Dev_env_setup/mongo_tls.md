# Mongodb SSL setup

## Generate CA

[Docs](https://www.mongodb.com/docs/manual/tutorial/configure-ssl/)

You can generate the Certificate Authority (CA) file and the certificate key file for MongoDB on a Linux server by using OpenSSL. Here are the step-by-step instructions:

### Generate the Certificate Authority (CA)

To generate the CA file, first, create a private key:

```
openssl genrsa -out ca.key 2048
```

Then create the root certificate:

```
openssl req -x509 -new -nodes -key ca.key -days 3650 -out ca.crt
```

During this step, you will be asked for some information, such as country, state, and common name. The common name can be your server's IP or domain name. The number of days (3650) is how long this certificate is valid, in this case, 10 years.

### Generate Server Certificate

Create the server private key:

```
openssl genrsa -out server.key 2048
```

vim ssl.conf to specify the server certificate attributes: CN, IP, DNS

```
[ req ]
distinguished_name = req_distinguished_name
x509_extensions    = v3_req
prompt             = no

[ req_distinguished_name ]
C = CA
ST = ON
L = Toronto
O = RM
OU = IT
CN = ca12

[v3_req]
keyUsage = critical, digitalSignature, keyAgreement
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[ alt_names ]
IP.1 = *************
IP.2 = 127.0.0.1
DNS.1 = ca12
```

Create a Certificate Signing Request (CSR) for the server. The common name here should match exactly with your MongoDB server host name:

```
openssl req -new -key server.key -out server.csr -config ssl.conf
```

Then, sign the server certificate with the CA:

```
openssl x509 -req -in server.csr -CA ../ca.crt -CAkey ../ca.key -CAcreateserial -out server.crt -days 3650
```

Concatenate the Server Certificate and Key

MongoDB requires the certificate and key to be in a single .pem file. Concatenate the certificate and key into one file:

bash

```
cat server.crt server.key > server.pem
```

You now have your CA file (ca.crt) and your certificate key file (server.pem). The files must be placed in a secure location, and you must update your MongoDB configuration file to use these files.

Remember to replace any placeholder with your actual server host names/IP addresses, and increase the key strength as needed. Ensure that all MongoDB servers, as well as any clients that will be connecting to MongoDB, trust the CA that was used to sign the certificates.

Also, make sure the server certificates contain the "Server Authentication" EKU, and all certificates set the "Key Encipherment" key usage. MongoDB will reject the connection if these attributes are not present.

Note: Always ensure that your private keys (server.key, ca.key) remain private. They should have strict file permissions set, and should not be shared or transmitted over insecure channels.

## config file

```
net:
  tls:
    mode: allowTLS
    certificateKeyFile: /etc/ssl/mongodb.pem
    certificateKeyFilePassword: Ca2Mongo
    CAFile: /etc/ssl/caToValidateClientCertificates.pem

    clusterFile: <string>
    clusterPassword: <string>
    clusterCAFile: <string>
    disabledProtocols: TLS1_0,TLS1_1
    allowConnectionsWithoutCertificates: true
```

- disabled
  The server does not use TLS/SSL.
- allowSSL
  Connections between servers do not use TLS/SSL. For incoming connections, the server accepts both TLS/SSL and non-TLS/non-SSL.
- preferSSL
  Connections between servers use TLS/SSL. For incoming connections, the server accepts both TLS/SSL and non-TLS/non-SSL.
- requireSSL
  The server uses and accepts only TLS/SSL encrypted connections.

### create user

```
db.createUser(
  {
      user: "r9",
      pwd: "r9",
      roles: [ "root" ]
  })
```

```
mongosh -u r9 -p r9 --authenticationDatabase admin --tls --host r9:27019 --tlsCAFile /etc/mongo/ca.crt --tlsCertificateKeyFile /etc/mongo/server.pem  --tlsAllowInvalidCertificates
mongosh --tls --tlsCAFile /etc/mongo/ca.crt --tlsCertificateKeyFile /etc/mongo/server.pem --host hostname:27018
```

openssl req -new -key server.key -out server.csr -config ssl.conf
