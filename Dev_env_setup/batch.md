# Notes:

Before run batches, check all remote mounted folders are correctly mounted.

# continuesly running batches

## main mongodb

nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init/routine force > ./logs/watchEs.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndPushNotify.coffee preload-model routine force >> ./logs/watchPropPushNotify.log 2>&1 &

## both rni and main

nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcreMan -t trebMan -t dta -t bcre -t rahb -t deleteEvow routine > ./logs/watchImport2Prop.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee batch/prop/condosSqft.coffee force >> ./logs/condoSqft.log 2>&1 &

- Error: There is no leader for this topic-partition as we are in the middle of a leadership election

## RNI depending batches

nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee crea force >> ./logs/creaPhoto.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee mlsImport/retsPhotoDownloadV2.coffee treb D23frx 'D2\$w123' force >> ./logs/trebPhotoV2frx.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee mlsImport/retsPhotoDownloadV2.coffee treb D20ywa '2F3\$9e7' force >> ./logs/trebPhotoV2ywa.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownloadV2.coffee idx D23frx 'D2\$w123' noPhotos force >> ./logs/trebIDX.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownloadV2.coffee evow EV23frx 'A7\$e162' noPhotos force >> ./logs/trebEVOW.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee mlsImport/rahbDownload.coffee -t 3a206267d8ca9f34a96236acf2f45a02 force >> ./logs/rahbDownload.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee mlsImport/rahbDownloadOfficeAndMember.coffee -t 3a206267d8ca9f34a96236acf2f45a02 force >> ./logs/rahbOfficeDownload.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee mlsImport/creaDownload.coffee BfHsE68ZfJtWrbeZXvPBpBxg WDbAEdaafc7CMJjnqjjWFNdh force >> ./logs/creaDownload.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee mlsImport/bcreDownload.coffee RETSFREDXIANG Hudr9ruWlbRaVop force >> ./logs/bcreDownload.log 2>&1 &
