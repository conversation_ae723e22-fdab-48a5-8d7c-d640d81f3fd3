## download python tgz

dnf install vim wget openssl-devel bzip2-devel libffi-devel -y
dnf -y groupinstall "Development Tools"
VERSION=3.11.6
wget https://www.python.org/ftp/python/$VERSION/Python-$VERSION.tgz
tar xvf Python-$VERSION.tgz
cd Python-$VERSION
./configure --enable-optimizations
make altinstall

## run on normal user

python3.11 -m pip install --upgrade pip
python3.11 -m venv py3.11
source py3.11/bin/activate
pip install --upgrade pip

cd pyml
pip install -r requirement.txt

- to deactive
  deactivate

## to run on gpu

https://rapids.ai/#quick-start

- in Jupyter
  %load_ext cudf.pandas
  import pandas as pd

- command line
  python -m cudf.pandas main.py

## on Unbuntu

sudo apt install wget build-essential libncursesw5-dev libssl-dev libsqlite3-dev tk-dev libgdbm-dev libc6-dev libbz2-dev libffi-dev zlib1g-dev
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt install python3.11
sudo apt-get install python3.11-dev python3.11-venv

python3.11 -m venv py3

source py3/bin/activate

pip install cudf-cu11 dask-cudf-cu11 --extra-index-url=https://pypi.nvidia.com
