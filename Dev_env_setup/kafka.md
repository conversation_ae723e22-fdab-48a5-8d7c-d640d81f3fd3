# Kafka

## create user

adduser -M kafka

## Kafka Security

[Docs](https://developer.confluent.io/courses/security/authentication-ssl-and-sasl-ssl/)

## Download kafka

mkdir /mnt/ssd0/kraft-combined-logs
cd /opt/
mkdir kafka
chown -R kafka:kafka /opt/kafka /mnt/ssd0/kraft-combined-logs
cd kafka
/bin/su - kafka

wget https://downloads.apache.org/kafka/3.5.1/kafka_2.12-3.5.1.tgz
tar xzf kafka_2.12-3.5.1.tgz
ln -s kafka_2.12-3.5.1 current
mkdir logs

## Setup Kafka

mkdir config
cp current/config/kraft/server.properties config/
vim config/server.properties

```
node.id=11
controller.quorum.voters=11@hostname:9193
log.dirs=/mnt/md0/kafka-combined-logs
```

mkdir /mnt/md0/kraft-combined-logs
chown kafka:kafka /mnt/md0/kraft-combined-logs
kafka-storage.sh random-uuid > config/uuid.txt
kafka-storage.sh format -t `cat config/uuid.txt` -c config/server.properties

nohup kafka-server-start.sh config/server.properties > logs/kafka.log 2>&1 &

## use SASL_SSL and add user and set up ACL

change lines in config/server.properties

```
listeners=SASL_SSL://ca4:9192,CONTROLLER://ca4:9193
security.inter.broker.protocol=SASL_SSL
#inter.broker.protocol=SASL_SSL
advertised.listeners=SASL_SSL://ca4:9192
sasl.enabled.mechanisms=PLAIN
sasl.mechanism.inter.broker.protocol=PLAIN
sasl.mechanism.controller.protocol=PLAIN
```

### setup TLS

cd /opt/kafka/config

**import CA to truststore**
keytool -keystore server.truststore.jks -alias CARoot -import -file /etc/mongo/ca.crt -storepass kRaft7 -keypass kRaft7

**create server keystore**
keytool -keystore server.keystore.jks -alias ca4 -validity 3650 -genkey -keyalg RSA -dname "CN=ca4, OU=IT, O=RM, L=Toronto, ST=ON, C=CA" -storepass kRaft7 -keypass kRaft7 -ext SAN=dns:localhost,ip:*************,ip:127.0.0.1,dns:ca4.realmaster.com,dns:ca4,dns:mq.realmaster.com,dns:mq

keytool -keystore server.keystore.jks -alias ca11 -validity 3650 -genkey -keyalg RSA -dname "CN=ca11, OU=IT, O=RM, L=Toronto, ST=ON, C=CA" -storepass kRaft7 -keypass kRaft7 -ext SAN=dns:localhost,ip:***************,ip:127.0.0.1,dns:ca11.realmaster.com,dns:ca11,dns:mq.realmaster.com,dns:mq

keytool -keystore server.keystore.jks -alias ca3 -validity 3650 -genkey -keyalg RSA -dname "CN=ca3, OU=IT, O=RM, L=Toronto, ST=ON, C=CA" -storepass kRaft7 -keypass kRaft7 -ext SAN=dns:localhost,ip:***************,ip:*************,ip:127.0.0.1,dns:ca3.realmaster.com,dns:ca3,dns:mq.realmaster.com,dns:mq

keytool -keystore server.keystore.jks -alias ca2 -validity 3650 -genkey -keyalg RSA -dname "CN=ca2, OU=IT, O=RM, L=Toronto, ST=ON, C=CA" -storepass kRaft7 -keypass kRaft7 -ext SAN=dns:localhost,ip:***********,ip:*************,ip:127.0.0.1,dns:ca2.realmaster.com,dns:ca2,dns:mq.realmaster.com,dns:mq

keytool -keystore server.keystore.jks -alias ca1 -validity 3650 -genkey -keyalg RSA -dname "CN=ca1, OU=IT, O=RM, L=Toronto, ST=ON, C=CA" -storepass kRaft7 -keypass kRaft7 -ext SAN=dns:localhost,ip:*************,ip:*************,ip:127.0.0.1,dns:ca1.realmaster.com,dns:ca1,dns:mq.realmaster.com,dns:mq

keytool -keystore server.keystore.jks -alias ca12 -validity 3650 -genkey -keyalg RSA -dname "CN=ca12, OU=IT, O=RM, L=Toronto, ST=ON, C=CA" -storepass kRaft7 -keypass kRaft7 -ext SAN=dns:localhost,ip:*************,ip:127.0.0.1,dns:ca12.realmaster.com,dns:ca12,dns:mqt.realmaster.com,dns:mqt

keytool -keystore server.keystore.jks -alias shf3 -validity 3650 -genkey -keyalg RSA -dname "CN=shf3, OU=IT, O=RM, L=Shanghai, ST=SH, C=CH" -storepass kRaft7 -keypass kRaft7 -ext SAN=dns:localhost,ip:*************,ip:127.0.0.1,dns:shf3.realmaster.com,dns:shf3

**create certificate signing request**
keytool -keystore server.keystore.jks -alias ca4 -certreq -file kafka.csr -storepass kRaft7 -keypass kRaft7

**sign the server certificate with the CA**
openssl x509 -req -in kafka.csr -CA ../ca.crt -CAkey ../ca.key -CAcreateserial -out kafka.crt -days 3650

**copy kafka.crt back to kafka server, then import CA and certificate to keystore**
keytool -keystore server.keystore.jks -alias CARoot -import -file /etc/mongo/ca.crt -storepass kRaft7 -keypass kRaft7
keytool -keystore server.keystore.jks -alias ca4 -import -file kafka.crt -storepass kRaft7 -keypass kRaft7

**change lines in config/server.properties**

```
ssl.keystore.location=/opt/kafka/config/server.keystore.jks
ssl.keystore.password=kRaft7
ssl.key.password=kRaft7
ssl.truststore.location=/opt/kafka/config/server.truststore.jks
ssl.truststore.password=kRaft7
ssl.client.auth=required

listener.name.sasl_ssl.ssl.keystore.location=/opt/kafka/config/server.keystore.jks
listener.name.sasl_ssl.ssl.keystore.password=kRaft7
listener.name.sasl_ssl.ssl.key.password=kRaft7
listener.name.sasl_ssl.ssl.truststore.location=/opt/kafka/config/server.truststore.jks
listener.name.sasl_ssl.ssl.truststore.password=kRaft7
```

https://developer.confluent.io/courses/security/authentication-ssl-and-sasl-ssl/

### setup jaas.conf

#### use server.properties

For standalone single server only.

```
listener.name.sasl_ssl.plain.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required \
    username="badmin" \
    password="AdPass23065!" \
    user_admin="AdPass23065!";
super.users=User:badmin
```

#### Separate jaas file

If use controller with SASL_SSL, must use separate jaas file.

vim config/jaas.conf

```
KafkaServer {
    org.apache.kafka.common.security.plain.PlainLoginModule required
    username="kadmin"
    password="AdPass23065!"
    user_kadmin="AdPass23065!";
};
```

username/password is for server to authenticate client
user\_(kadmin) is for client to authenticate on server. Both are needed for sasl*ssl on controller port.
multiple users*() can be added for different clients.

export KAFKA_OPTS="-Djava.security.auth.login.config=/opt/kafka/config/jaas.conf"

## start server

```
nohup kafka-server-start.sh config/server.properties > logs/kafka.log 2>&1 &
```

## config client

vim config/client.properties

```
security.protocol=SASL_SSL
sasl.mechanism=PLAIN
ssl.truststore.location=/opt/kafka/config/server.truststore.jks
ssl.truststore.password=kRaft7
ssl.key.password=kRaft7
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required \
    username="kadmin" \
    password="AdPass23065!";
```

or use separate jaas file: client-jaas.conf

```
KafkaClient {
    org.apache.kafka.common.security.plain.PlainLoginModule required
    username="kadmin"
    password="AdPass23065"
    user_admin="AdPass23065";
};
```

export KAFKA_OPTS="-Djava.security.auth.login.config=/opt/kafka/config/client-jaas.conf"

## check users

kafka-topics.sh --create --topic test1 --bootstrap-server ca4:9192 --command-config /opt/kafka/config/client.properties
kafka-topics.sh --list --bootstrap-server ca4:9192 --command-config /opt/kafka/config/client.properties
kafka-console-producer.sh --bootstrap-server ca4:9192 --topic xxx

## CONTROLLER use SASL_SSL

```
process.roles=broker,controller
node.id=41
controller.quorum.voters=41@ca4:9194
listeners=PLAINTEXT://ca4:9192,SASL_SSL://ca4:9193,CONTROLLER://ca4:9194
advertised.listeners=PLAINTEXT://ca4:9192,SASL_SSL://ca4:9193
controller.listener.names=CONTROLLER

listener.security.protocol.map=CONTROLLER:SASL_SSL,PLAINTEXT:PLAINTEXT,SSL:SSL,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_SSL:SASL_SSL

```

- Must use [separate jaas file](#separate-jaas-file) for controller.
- To support both SASL_SSL and PLAINTEXT, must use advertised.listeners=PLAINTEXT://ca4:9192,SASL_SSL://ca4:9193 to advertise both ports.

## to add systemd service

follow [Download kafka](#download-kafka) then [Setup kafka](#setup-kafka) but use following settings.

vim /etc/systemd/system/kafka.service

```
[Unit]
Description=Apache Kafka Server in Kraft Mode
After=NetworkManager.target

[Service]
Type=simple
User=kafka
Group=kafka
Environment="KAFKA_OPTS=-Djava.security.auth.login.config=/opt/kafka/config/jaas.conf"
#Environment="KAFKA_LOG4J_OPTS=-Dlog4j.configuration=file:/opt/kafka/config/log4j.properties"
ExecStart=/bin/sh -c '/opt/kafka/current/bin/kafka-server-start.sh /opt/kafka/config/server.properties > /opt/kafka/logs/kafka.log 2>&1'
ExecStop=/opt/kafka/current/bin/kafka-server-stop.sh
#StandardOutput=file:/opt/kafka/logs/kafka.log
#StandardError=inherit
#FileCreateMode=0640
Restart=on-abnormal
TimeoutStopSec=10
RestartSec=10

[Install]
WantedBy=multi-user.target

```

systemctl daemon-reload
systemctl enable --now kafka

## to add more kafka nodes to a cluster

- make sure all cluster members use the same uuid to format the kraft-combined-logs folder

add other servers to quorum for all servers

```
controller.quorum.voters=111@ca11:9194,41@ca4:9194
```

rm /mnt/{md0,ssd0}/kraft-combined-logs/\_\_cluster_metadata-0/quorum-state

systemctl restart kafka

## Overall settings for kRaft server cluster

- support both plaintext and SASL_SSL+PLAIN and controllers
- listeners without hostname to listen on all ips
- user security.inter.broker.protocal to secure connection between servers
- advertised.listeners shall include both PLAIN and SASL_SSL ports
- change listener.security.protocol.map to secure controller communications
- check controller.quorum.voters match with listeners port

config/server.properties

```
process.roles=broker,controller
node.id=31
controller.quorum.voters=41@ca4:9194,111@ca11:9194,31@ca3:9194
listeners=PLAINTEXT://:9192,SASL_SSL://:9193,CONTROLLER://:9194
#inter.broker.listener.name=PLAINTEXT
security.inter.broker.protocol=SASL_SSL
sasl.enabled.mechanisms=PLAIN
sasl.mechanism.inter.broker.protocol=PLAIN
sasl.mechanism.controller.protocol=PLAIN

# TLS setup fred
ssl.keystore.location=/opt/kafka/config/server.keystore.jks
ssl.keystore.password=kRaft7
ssl.key.password=kRaft7
ssl.truststore.location=/opt/kafka/config/server.truststore.jks
ssl.truststore.password=kRaft7
ssl.client.auth=required
#ssl.client.auth=none
#ssl.endpoint.identification.algorithm=''

#listener.name.sasl_ssl.ssl.client.auth=none
listener.name.sasl_ssl.ssl.keystore.location=/opt/kafka/config/server.keystore.jks
listener.name.sasl_ssl.ssl.keystore.password=kRaft7
listener.name.sasl_ssl.ssl.key.password=kRaft7
listener.name.sasl_ssl.ssl.truststore.location=/opt/kafka/config/server.truststore.jks
listener.name.sasl_ssl.ssl.truststore.password=kRaft7
listener.name.sasl_ssl.ssl.client.auth=required

#advertised.listeners=PLAINTEXT://localhost:9192
advertised.listeners=PLAINTEXT://ca3:9192,SASL_SSL://ca3:9193

#listener.security.protocol.map=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,SSL:SSL,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_SSL:SASL_SSL
listener.security.protocol.map=CONTROLLER:SASL_SSL,PLAINTEXT:PLAINTEXT,SSL:SSL,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_SSL:SASL_SSL

log.dirs=/mnt/md0/kraft-combined-logs
```

copy uuid from existing cluster members to config/uuid.txt
./current/bin/kafka-storage.sh format -t `cat config/uuid.txt` -c config/server.properties

follow [TSL setup](#setup-tls), check ip and server names

follow [Separate jaas config file](#separate-jaas-file), check user/password

change /etc/nftables/main.nft on all servers and reload settings

follow [Add systemd service](#to-add-systemd-service), check path name

export KAFKA_OPTS="-Djava.security.auth.login.config=/opt/kafka/config/jaas.conf"
./current/bin/kafka-server-start.sh config/server.properties

follow [to add nodes to cluster](#to-add-more-kafka-nodes-to-a-cluster)

## Mirror marker

cd /opt/kafka/config
cp ../current/config/connect-mirror-maker.properties connect-main-public.properties
vim connect-main-public.properties

```
# specify any number of cluster aliases
clusters = Main, Public

# connection information for each cluster
# This is a comma separated host:port pairs for each cluster
# for e.g. "A_host1:9092, A_host2:9092, A_host3:9092"
#A.bootstrap.servers = A_host1:9092, A_host2:9092, A_host3:9092
Main.bootstrap.servers = ca1:9192, ca2:9192, ca3:9192
#B.bootstrap.servers = B_host1:9092, B_host2:9092, B_host3:9092
Public.bootstrap.servers = ca4:9193, ca11:9193

Public.client.id=main2public
Public.group.id=main2public
Public.ssl.enabled.protocols=TLSv1.3
Public.ssl.truststore.location=/opt/kafka/config/server.truststore.jks
Public.ssl.truststore.password=kRaft7
Public.ssl.protocol=TLSv1.3
Public.security.protocol=SASL_SSL
Public.sasl.mechanism=PLAIN
Public.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="padmin" password="2i5k&E1HS9FL";

exclude.internal.topics=true

# enable and configure individual replication flows
Main->Public.enabled = true

# regex which defines which topics gets replicated. For eg "foo-.*"
#Main->Public.topics = .*
Main->Public.topics = req.*

Public->Main.enabled = true
Public->Main.topics = resp.*

# Setting replication factor of newly created remote topics
replication.factor=1

############################# Internal Topic Settings  #############################
# The replication factor for mm2 internal topics "heartbeats", "B.checkpoints.internal" and
# "mm2-offset-syncs.B.internal"
# For anything other than development testing, a value greater than 1 is recommended to ensure availability such as 3.
checkpoints.topic.replication.factor=1
heartbeats.topic.replication.factor=1
offset-syncs.topic.replication.factor=1

# The replication factor for connect internal topics "mm2-configs.B.internal", "mm2-offsets.B.internal" and
# "mm2-status.B.internal"
# For anything other than development testing, a value greater than 1 is recommended to ensure availability such as 3.
offset.storage.replication.factor=1
status.storage.replication.factor=1
config.storage.replication.factor=1

# customize as needed
# replication.policy.separator = _
# sync.topic.acls.enabled = false
# emit.heartbeats.interval.seconds = 5

```

vim /etc/systemd/system/kmm.service

```
[Unit]
Description=Kafka Mirror Maker
After=kafka.target

[Service]
Type=simple
User=kafka
Group=kafka
CPUSchedulingPriority=2
IOSchedulingClass=idle
IOSchedulingPriority=7
BlockIOWeight=100
ExecStart=/bin/sh -c '/opt/kafka/current/bin/connect-mirror-maker.sh /opt/kafka/config/connect-main-public.properties > /opt/kafka/logs/kmm.log 2>&1'
Restart=on-abnormal
TimeoutStopSec=10
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## TODO: logrotate
