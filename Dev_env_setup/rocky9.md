# Rocky Linux 9

## install packages

```
dnf install -y epel-release
dnf install -y vim git git-lfs sysstat net-tools nethogs htop bind-utils wget
dnf install -y esmtp-local-delivery
dnf install -y fio smartmontools pinentry-tty zip
```

## btrfs

[docs](https://elrepo.org/tiki/kernel-lt)

```

yum install https://www.elrepo.org/elrepo-release-9.el9.elrepo.noarch.rpm
dnf --enablerepo=elrepo-kernel install kernel-lt
reboot
```

### omz

```
dnf install -y zsh
sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"
omz plugin enable wd
wd list
sed -i 's/ZSH_THEME="robbyrussell"/ZSH_THEME="jtriley"/g' ~/.zshrc

sed -i 's/ZSH_THEME="robbyrussell"/ZSH_THEME="aussiegeek"/g' ~/.zshrc
sed -i 's/ZSH_THEME="aussiegeek"/ZSH_THEME="jtriley"/g' ~/.zshrc

source ~/.zshrc
```

### test io performance

```
smartctl -a /dev/sda

fio --randrepeat=1 --ioengine=libaio --direct=1 --gtod_reduce=1 --name=test --filename=random_read_write.fio --bs=4k --iodepth=64 --size=4G --readwrite=randrw --rwmixread=75
```

## tighten security

sudo bash

### create a user

useradd -m -G wheel user1
or
useradd user1
passwd user1 or setup .ssh/authorized_keys
logout and relogin as new user
login as new user and /bin/su - to root
userdel -r rocky
usermod -u 1000 user1
groupmod -g 1000 user1

### disable sudo for all users

setup root password

    passwd

visudo

    comment out the line that says %admin ALL=(ALL) ALL

    # Defaults always_query_group_plugin

    # rocky ALL=(ALL) NOPASSWD: ALL

### sshd_config and sshd_config.d/50-redhat.conf

```
PasswordAuthentication no
PermitRootLogin no
AllowUsers user1
```

systemctl reload sshd

### edit vlan

nmtui
systemctl restart NetworkManager
nmcli -o

### secondary ip

dnf install NetworkManager-initscripts-updown

### nftables

systemctl disable --now firewalld
systemctl mask --now firewalld

dnf install -y nftables
systemctl enable --now nftables

[Guide](https://www.linode.com/docs/guides/how-to-use-nftables/)
[Beginner Youtube](https://www.youtube.com/watch?v=_A-Q6yTMX0g)
[REHL](https://access.redhat.com/documentation/en-us/red_hat_enterprise_linux/8/html/configuring_and_managing_networking/getting-started-with-nftables_configuring-and-managing-networking)
[Wiki](https://wiki.nftables.org/wiki-nftables/index.php/Quick_reference-nftables_in_10_minutes)

edit /etc/sysconfig/nftables.conf

    include "/etc/nftables/main.nft"

mv /etc/nftables/main.nft /etc/nftables/main.nft_orig

/etc/nftables/main.nft

```

#!/usr/sbin/nft -f

flush ruleset
define trustedIf = "enp3s0f1"
define trustedIps = *************-*************
define sshIps = ***********
define publicIps = ***************
define publicIf = "enp3s0f0"

# shf1, shf2, vg20

define remoteMongoServers = { **************, ************, ************* }
define mongoPort = 27001-27020
define knock_port2 = 13456
defind knock_port1 = 876

table inet filter {

  # public services to allow

  set allowed_services {
    type inet_service
    elements = { http, https }
  }

  # door knock for ssh
  set knock_stage1 {
    type ipv4_addr
    timeout 10s
    size 10000
  }
  set knock_stage2 {
    type ipv4_addr
    timeout 10s
    size 10000
  }


  chain input{
  type filter hook input priority 0;
  policy drop;

      # accept any localhost traffic
      iif lo accept;

      # established/related connections
      ct state established,related accept;

      # invalid connections
      ct state invalid drop;

      # no ping flood
      ip6 nexthdr icmpv6 icmpv6 type { nd-neighbor-solicit, nd-router-advert, nd-neighbor-advert } limit rate 2/second burst 5 packets accept;
      ip protocol icmp icmp type { destination-unreachable, echo-reply, echo-request, router-advertisement, router-solicitation, time-exceeded } limit rate 2/second burst 5 packets accept;

      # trusted interface and ips
      iif $trustedIf ip saddr $trustedIps accept;

      # ssh from anywhere to specific ip
      tcp dport 22 ip daddr $sshIps accept;

    #  # Or use Port knocking sequence
    #  # Stage 1: First knock on port $knock_port1, add source IP to set
    #  tcp dport $knock_port1 ip daddr $sshIps add @knock_stage1 { ip saddr }
    #
    #  # Stage 2: Second knock on port $knock_port2, proceed to final stage
    #  tcp dport $knock_port2 ip daddr $sshIps ip saddr @knock_stage1 add @knock_stage2 { ip saddr }
    #
    #  # Allow SSH if the source IP has completed the knocking sequence
    #  tcp dport 22 ip daddr $sshIps ip saddr @knock_stage2 accept

      # allow remote mongo servers to connect mongo ports
      tcp dport $mongoPort ip saddr $remoteMongoServers accept;

      # public services on publicIf and publicIps
      iif $publicIf ip daddr $publicIps tcp dport @allowed_services accept

  }
  chain forward{
  type filter hook forward priority 0; policy accept;
  }
  chain output{
  type filter hook output priority 0; policy accept;
  }
}

```

nft for ca10:

```
# drop any existing nftables ruleset
flush ruleset

define sshIps = ***************
define publicIps = **************
define publicIf = "eno3"

define privateIf = "eno3"
define privateIps = ***************

# ca1,2,6
define trustedIps = { ************, ***********, ************}

table inet filter {

  # public services to allow

  set allowed_services {
    type inet_service
    elements = { http, https }
  }

  chain input{
  type filter hook input priority 0;
  policy drop;

      # accept any localhost traffic
      iif lo accept;

      # established/related connections
      ct state established,related accept;

      # invalid connections
      ct state invalid drop;

      # no ping flood
      ip6 nexthdr icmpv6 icmpv6 type { nd-neighbor-solicit, nd-router-advert, nd-neighbor-advert } limit rate 2/second burst 5 packets accept;
      ip protocol icmp icmp type { destination-unreachable, echo-reply, echo-request, router-advertisement, router-solicitation, time-exceeded } limit rate 2/second burst 5 packets accept;

      # trusted interface and ips
      iif $privateIf ip saddr $trustedIps ip daddr $privateIps accept;

      # ssh from anywhere to specific ip
      tcp dport 22 ip daddr $sshIps accept;

      # allow remote mongo servers to connect mongo ports
      # tcp dport $mongoPort ip saddr $remoteMongoServers accept;

      # public services on publicIf and publicIps
      iif $publicIf ip daddr $publicIps tcp dport @allowed_services accept

  }
  chain forward{
  type filter hook forward priority 0; policy accept;
  }
  chain output{
  type filter hook output priority 0; policy accept;
  }
}
```

systemctl restart nftables

nft list ruleset

https://docs.rockylinux.org/guides/security/dnf_automatic/

### to connect to ssh server with door knocking

use shell file like:

```
#!/bin/bash

# Configuration
SERVER_IP="server-ip/hostname"
SSH_USER="user-name"
knock_port1=11111
knock_port2=22222

# Knocking ports
KNOCK_PORTS=($knock_port1 $knock_port2)

# Knock on each port
for port in "${KNOCK_PORTS[@]}"; do
  nc -z -G 1 $SERVER_IP $port
#  sleep 1  # Wait a bit before the next knock
done

# Connect to SSH
#ssh -L 27018:localhost:27018 -p 1023 ca5@ca5sh
#ssh ca5m@ca5sh
ssh $SSH_USER@$SERVER_IP
```

## setup timezone

https://www.rootusers.com/how-to-synchronize-time-in-linux-with-ntp-peers/
dnf install -y chrony
systemctl enable --now chronyd
vi /etc/chrony.conf

```
pool 0.amazon.pool.ntp.org iburst
pool 1.amazon.pool.ntp.org iburst
pool 2.amazon.pool.ntp.org iburst
pool 3.amazon.pool.ntp.org iburst
```

systemctl restart chronyd
chronyc sources
timedatectl
timedatectl set-timezone America/Toronto
timedatectl set-ntp true

## create mdadm raid0

mdadm --create --verbose /dev/md0 --level=0 --raid-devices=2 /dev/sd[a-b]1

mdadm --detail --scan --verbose

### create filesystem xfs for lots of small files

mkfs.xfs -f -d agcount=32 -l size=128m,version=2 /dev/md0

### add to fstab with noatime

blkid /dev/md0
UUID=XXXXX /mnt/md0 xfs defaults,noatime 0 0

## install services

### nfs

yum -y install nfs-utils

edit /etc/exports
/mnt/raid10 ***********/***********(rw,sync,no_subtree_check,no_root_squash)
/mnt/raid10 **************/255.255.255.2555(rw,sync,no_subtree_check,no_root_squash)

systemctl restart nfs-server
systemctl enable --now nfs-server

on the client machine:
mount server-ip:/mnt/raid10 /mnt/client-raid10

copy files from client to server with limited bandwidth in KBPS
rsync -av --bwlimit=1000 --progress /foo /bar

**note** client uid/gid must match a server uid/gid existing on the server

### install mongodb

vim /etc/yum.repos.d/mongodb-org.repo

```

[mongodb-org-7.0]
name=MongoDB Repository
baseurl=https://repo.mongodb.org/yum/redhat/$releasever/mongodb-org/7.0/x86_64/
gpgcheck=1
enabled=1
gpgkey=https://www.mongodb.org/static/pgp/server-7.0.asc

```

dnf repolist
dnf install -y mongodb-org

/etc/mongod37.conf

```
# mongod37.conf

# for documentation of all options, see:
#   http://docs.mongodb.org/manual/reference/configuration-options/

# where to write logging data.
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod37.log

# Where and how to store data.
storage:
#  dbPath: /var/lib/mongo37
  dbPath: /mnt/raid0/mongo37
  journal:
    enabled: true
#  engine:
#  wiredTiger:
  wiredTiger:
    engineConfig:
      cacheSizeGB: 2

# how the process runs
processManagement:
  fork: true  # fork and run in background
  pidFilePath: /var/run/mongodb/mongod37.pid  # location of pidfile
  timeZoneInfo: /usr/share/zoneinfo

# network interfaces
net:
  port: 27037
  bindIp: 127.0.0.1,*************,************  # Enter 0.0.0.0,:: to bind to all IPv4 and IPv6 addresses or, alternatively, use the net.bindIpAll setting.

security:
#  keyFile: /var/lib/mongo37/mongoKey37
  keyFile: /mnt/raid0/mongo37/mongoKey37
  authorization: disabled
replication:
  oplogSizeMB: 32000
  replSetName: prodRni

#net:
#   tls:
#      mode: allowTLS # preferTLS #requireTLS
#      certificateKeyFile: /etc/ssl/ca2mongo.pem
#      certificateKeyFilePassword: Ca2Mongo
```

#### mongo TLS

[refer mongo TLS](./mongo_tls.md)

```
mkdir /etc/mongo
cd /etc/mongo
openssl genrsa -out server.key 2048
openssl req -new -key server.key -out server.csr
```

on ca2 root, paste server.csr into /root/ca/SERVER/server.csr:
cd /root/ca/SERVER/

```
openssl x509 -req -in server.csr -CA ../ca.crt -CAkey ../ca.key -CAcreateserial -out server.crt -days 3650
```

paste back server.crt and ca.crt to /etc/mongo/

```
cat server.crt server.key > server.pem
```

add to /etc/mongod.conf 'net' section:

```
  tls:
    mode: allowTLS # preferTLS
    certificateKeyFile: /etc/mongo/server.pem
    CAFile: /etc/mongo/ca.crt
    disabledProtocols: TLS1_0,TLS1_1
    allowConnectionsWithoutCertificates: true
```

{"t":{"$date":"2023-08-09T21:27:32.034-04:00"},"s":"W",  "c":"CONTROL",  "id":22178,   "ctx":"initandlisten","msg":"/sys/kernel/mm/transparent_hugepage/enabled is 'always'. We suggest setting it to 'never'","tags":["startupWarnings"]}
{"t":{"$date":"2023-08-09T21:27:32.034-04:00"},"s":"W", "c":"CONTROL", "id":5123300, "ctx":"initandlisten","msg":"vm.max_map_count is too low","attr":{"currentValue":65530,"recommendedMinimum":102400,"maxConns":51200},"tags":["startupWarnings"]}

### install redis, elasticsearch

- follow documentation in source docs, save password

update-crypto-policies --set LEGACY
rpm --import https://artifacts.elastic.co/GPG-KEY-elasticsearch
vim /etc/yum.repos.d/elasticsearch.repo

```
[elasticsearch]
name=Elasticsearch repository for 8.x packages
baseurl=https://artifacts.elastic.co/packages/8.x/yum
gpgcheck=1
gpgkey=https://artifacts.elastic.co/GPG-KEY-elasticsearch
enabled=0
autorefresh=1
type=rpm-md
```

dnf install --enablerepo=elasticsearch -y elasticsearch
update-crypto-policies --set DEFAULT

- ca1: elasticsearch password: feFJzkfixEr367gVVoVx
- shf3: a24j7xS1T11IsXZz4V4m
- ca12: E=0\*4p9URuWf-jqZpg1b
- ca3: 3R=KHvAK2Y\*litHugYiJ
- ca2: TudWuRoiQlbrUGIuHxZD
- ca5: B-H_PJ9Lzpmht+ndlRYy

dnf install redis -y
vim /etc/redis/redis.conf

```
> maxmemory 200mb
> appendfsync no
```

systemctl enable --now redis

### install rkhunter

dnf install rkhunter -y
vi /etc/rkhunter.conf

```

#MAIL-ON-WARNING=me@mydomain root@mydomain
MAIL_CMD=mail -s "[rkhunter] Warnings found for ${HOST_NAME}"

# pgsql

ALLOWDEVFILE=/dev/shm/PostgreSQL.\*

ALLOW_SSH_ROOT_USER=no

```

rkhunter --update
rkhunter --propupd
rkhunter --check

### logrotate

vim /etc/logrotate.conf

```

# rotate log files weekly

#weekly
daily

# keep 4 weeks worth of backlogs

# rotate 4

rotate 10

# create new (empty) log files after rotating old ones

create

# use date as a suffix of the rotated file

dateext

# uncomment this if you want your log files compressed

compress

# packages drop log rotation information into this directory

include /etc/logrotate.d

```

vim /etc/logrotate.d/cmate

```

/home/<USER>/rmcfg/logs/\*log {
    daily
    copytruncate
    rotate 10
    missingok
    notifempty
    compress
    sharedscripts
    postrotate
    endscript
    su ca2m ca2m
}

```

vim /etc/logrotate.d/mongodb

```

/var/log/mongodb/\*.log {
    daily
    missingok
    rotate 7
    copytruncate
    compress
    notifempty
    sharedscripts
    postrotate
    if [ -f /var/run/mongodb/mongod.pid ]; then
        kill -SIGUSR1 `cat /var/run/mongodb/mongod.pid`
    fi
    endscript
    su mongod mongod
}

```

### copy files

[docs](https://basila.medium.com/fastest-way-to-copy-a-directory-in-linux-40611d2c5aa4)

cd /path/to/SOURCE_FOLDER; tar cf - . | (cd /path/to/DESTINATION_FOLDER; tar xvf -)
tar cf - . | pv | ssh user@host "(cd /dest/; tar xf - )"
cd /mnt/raid0/treb/
tar cf - . | pv | ssh ca10u@ca10 "(cd /mnt/lv0/mlsimgs/treb/; tar xf - )"

#### use rsync to copy files

rsync -aP /mnt/md0/mlsimgs/treb/mls/ ca10u@ca10:/mnt/lv0/mlsimgs/treb/mls/ > ~/logs/rsyncCa10treb.log 2>&1 &
rsync -aP /mnt/md0/mlsimgs/crea/ddf/ ca10u@ca10:/mnt/lv0/mlsimgs/crea/ddf/ > ~/logs/rsyncCa10ddf.log 2>&1 &

rsync -aP /mnt/md0/mlsimgs/treb/mls/ ca2m@ca2:/mnt/md0/mlsimgs/treb/mls/ > ~/logs/rsyncCa2Treb.log 2>&1 &

### create lvm

cfdisk or fdisk /dev/sda
pvcreate /dev/sd{a,b,c,d}1
vgcreate vghd /dev/sd{a,b,c,d}1
lvcreate -l 100%FREE -n lvhd vghd

### build cache

[main docs](https://manpages.ubuntu.com/manpages/impish/man7/lvmcache.7.html)
[Docs](https://blog.delouw.ch/2020/01/29/using-lvm-cache-for-storage-tiering/)

```
pvcreate /dev/nvme0n1
vgextend vghd /dev/nvm0n1
lvcreate -n lvcache -l 100%FREE vghd /dev/nvme0n1
lvconvert --type cache --cachevol vghd/lvcache vghd/lvhd

lvcreate -n lvhd -l 100%FREE vg /dev/sd{a,b,c,d}1
lvs -a
lvconvert --type cache --chunksize 512 --cachevol vg/lvcache vg/lvhd
# lvconvert --type cache-pool --poolmetadata vg/lvcache vghd/lvhd
lvs -o+cache_mode vg/lvhd
lvchange --cachemode writeback vg/lvhd
lvs -o+cache_policy,cache_settings vg/lvhd

# to remove cache
lvchange --uncache vg/lvhd
```

### create filesystem

[source](https://cbs.centos.org/koji/buildinfo?buildID=42790)

```
wget https://cbs.centos.org/kojifiles/packages/btrfs-progs/5.16.2/1.3.el9/x86_64/btrfs-progs-5.16.2-1.3.el9.x86_64.rpm
rpm --install btrfs-progs-5.16.2-1.3.el9.x86_64.rpm

mkfs -t btrfs /dev/vg/lvhd

```

vim /etc/fstab

```
/dev/vg/lvhd	/mnt/lv0		btrfs	rw,nosuid,nodev,noexec,auto,user,noatime,async,nofail	0	0
```

systemctl daemon-reload
mount -a

### redis

dnf install redis -y
vi /etc/redis/redis.conf

```
maxmemory 200mb
appendfsync no
```

systemctl enable --now redis

### track network issues

mtr -o "J M X LSR NA B W V" -wbzc 100 51.79.191.162

### shadowsocket

https://hackernoon.com/installing-shadowsocks-rust-a-secure-open-source-proxy-server-better-than-vpn

wget https://github.com/shadowsocks/shadowsocks-rust/releases/download/v1.15.4/shadowsocks-v1.15.4.x86_64-unknown-linux-gnu.tar.xz
tar xf shadowsocks-v1.15.4.x86_64-unknown-linux-gnu.tar.xz -C /sbin/

vim /etc/shadowsocks.json

```
{
    "server":"0.0.0.0",
    "server_port":27098,
    "local_port":45035,
    "password":"p8ru59fNqDZU",
    "timeout":600,
    "method":"chacha20-ietf-poly1305"
}
```

use normal user to run ssserver
nohup /sbin/ssserver -c /etc/shadowsocks.json -d > $HOME/ss.log 2>&1 &

Client:
https://github.com/shadowsocks/ShadowsocksX-NG

#### haproxy

dnf install haproxy -y

vim /etc/rsyslog.conf
uncomment

```
# Provides UDP syslog reception
# for parameters see http://www.rsyslog.com/doc/imudp.html
module(load="imudp") # needs to be done just once
input(type="imudp" port="514")
```

add

```
# Don't log private authentication messages!
local2.none                 /var/log/messages
local2.* /var/log/haproxy.log
```

rsyslogd -N1
systemctl restart rsyslog

### postfix

dnf install postfix s-nail -y

vim /etc/postfix/main.cf

```
myhostname = mta1.domain.com
mydomain = domain.com
myorigin = $mydomain
mydestination = $myhostname, localhost.$mydomain, localhost, $mydomain
inet_interfaces = all
inet_protocols = ipv4
mynetworks = ***********/24, *********/8
```
