nft/main.cfg

``` 
#!/usr/sbin/nft -f

flush ruleset
define trustedIf = "enp9s0f1"
define trustedIps = *************-*************
define sshIps = **************
define publicIps = **************
define publicIf = "enp9s0f0"
# vg20, ap1, shf3, shf4, ca4, ca11
define remoteServers = {*************, *************, ***************, **************, *************, ***************}
define servicePorts = {27017,27037,9193,9194}

# ca5L, ca5, ca4, ca11
define kafkaClients = {*************, *************, *************, ***************}
define kafkaPorts = {9192}

# door knock ports
define knock_port2 = 13456
define knock_port1 = 876

table inet filter {

  # public services to allow
  set allowed_services {
    type inet_service
    elements = { http, https }
  }

  # door knock for ssh
  set knock_stage1 {
    type ipv4_addr
    timeout 10s
    size 10000
  }
  set knock_stage2 {
    type ipv4_addr
    timeout 10s
    size 10000
  }

  chain input{
    type filter hook input priority 0;
    policy drop;

    # accept any localhost traffic
    iif lo accept;

    # established/related connections
    ct state established,related accept;

    # invalid connections
    ct state invalid drop;

    # no ping flood
    ip6 nexthdr icmpv6 icmpv6 type { nd-neighbor-solicit, nd-router-advert, nd-neighbor-advert } limit rate 2/second burst 5 packets accept;
    ip protocol icmp icmp type { destination-unreachable, echo-reply, echo-request, router-advertisement, router-solicitation, time-exceeded } limit rate 2/second burst 5 packets accept;

    # trusted interface and ips
    iif $trustedIf ip saddr $trustedIps accept;

    # ssh from anywhere to specific ip
    tcp dport 22 ip daddr $sshIps ip saddr $remoteServers accept;

      # Or use Port knocking sequence
      # Stage 1: First knock on port $knock_port1, add source IP to set
      tcp dport $knock_port1 ip daddr $sshIps add @knock_stage1 { ip saddr }
    
      # Stage 2: Second knock on port $knock_port2, proceed to final stage
      tcp dport $knock_port2 ip daddr $sshIps ip saddr @knock_stage1 add @knock_stage2 { ip saddr }
    
      # Allow SSH if the source IP has completed the knocking sequence
      tcp dport 22 ip daddr $sshIps ip saddr @knock_stage2 accept

      # allow remote servers to connect mongo and kafka public ports
      tcp dport $servicePorts ip saddr $remoteServers accept;
      
      # allow remote kafka clients to connect without ssl
      tcp dport $kafkaPorts ip saddr $kafkaClients accept;

    # public services on publicIf and publicIps
    iif $publicIf ip daddr $publicIps tcp dport @allowed_services accept

    jump log_dropped;
  }
  chain log_dropped {
    log prefix "Dropped packet: " flags all;
  }
  chain forward{
    type filter hook forward priority 0; policy drop;
  }
  chain output{
    type filter hook output priority 0; policy accept;
  }
}
```

