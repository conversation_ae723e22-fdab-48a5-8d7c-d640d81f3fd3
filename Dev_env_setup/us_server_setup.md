## haproxy

- mongo back to primary
- ss to shf
  vim /etc/haproxy/haproxy.cfg

```
#---------------------------------------------------------------------
# Example configuration for a possible web application.  See the
# full configuration options online.
#
#   http://haproxy.1wt.eu/download/1.4/doc/configuration.txt
#
#---------------------------------------------------------------------

#---------------------------------------------------------------------
# Global settings
#---------------------------------------------------------------------
global
    # to have these messages end up in /var/log/haproxy.log you will
    # need to:
    #
    # 1) configure syslog to accept network log events.  This is done
    #    by adding the '-r' option to the SYSLOGD_OPTIONS in
    #    /etc/sysconfig/syslog
    #
    # 2) configure local2 events to go to the /var/log/haproxy.log
    #   file. A line like the following can be added to
    #   /etc/sysconfig/syslog
    #
    #    local2.*                       /var/log/haproxy.log
    #
    #log         127.0.0.1 local2
    log		/dev/log local0

    chroot      /var/lib/haproxy
    pidfile     /var/run/haproxy.pid
    maxconn     4000
    user        haproxy
    group       haproxy
    daemon

    # turn on stats unix socket
    stats socket /var/lib/haproxy/stats

#---------------------------------------------------------------------
# common defaults that all the 'listen' and 'backend' sections will
# use if not designated in their block
#---------------------------------------------------------------------
defaults
    #mode                    http
    mode                    tcp
    log                     global
    #option                  httplog
    option                  dontlognull
    #option http-server-close
    #option forwardfor       except 127.0.0.0/8
    #option                  redispatch
    #retries                 3
    #timeout http-request    10s
    #timeout queue           1m
    timeout connect         10s
    timeout client          1m
    timeout server          1m
    timeout http-keep-alive 10s
    timeout check           10s
    maxconn                 3000

#---------------------------------------------------------------------
# main frontend which proxys to the backends
#---------------------------------------------------------------------
#frontend  main *:5000
#    acl url_static       path_beg       -i /static /images /javascript /stylesheets
#    acl url_static       path_end       -i .jpg .gif .png .css .js

#    use_backend static          if url_static
#    default_backend             app

#---------------------------------------------------------------------
# static backend for serving up images, stylesheets and such
#---------------------------------------------------------------------
#backend static
#    balance     roundrobin
#    server      static 127.0.0.1:4331 check

#---------------------------------------------------------------------
# round robin balancing between the various backends
#---------------------------------------------------------------------
#backend app
#    balance     roundrobin
#    server  app1 127.0.0.1:5001 check
#    server  app2 127.0.0.1:5002 check
#    server  app3 127.0.0.1:5003 check
#    server  app4 127.0.0.1:5004 check


frontend ss-in
        bind *:27098
        default_backend ss-out

backend ss-out
        server shf4 *************:27098 maxconn 100


frontend mongoA
   bind 192.168.10.226:27027
   default_backend mongoca6

frontend mongo012
   bind *:27012
   default_backend mongoca2

frontend mongo011
   bind *:27011
   default_backend mongoca1

frontend mongo003
   bind *:27003
   default_backend mongoshf3

backend mongoca1
   server ca1 51.161.87.16:27011 maxconn 3000

backend mongoca2
   server ca2 15.235.9.34:27012 maxconn 3000

backend mongoshf3
   server shf3 192.168.3.123:27003 maxconn 3000

backend mongoca6
   server ca6 ************:27027 maxconn 3000

backend mongoother
   server other 127.0.0.1:27027 maxconn 1
```

## shadowsocks

- us

/etc/shadowsocks.json

```
{
    "server":"0.0.0.0",
    "server_port":27099,
    "local_port":45035,
    "password":"password",
    "timeout":600,
    "method":"chacha20-ietf-poly1305"
}
```

## nginx for mptl

from CDN to google map tile

https://mptl.realmaster.cn/vt/lyrs=m&x={x}&y={y}&z={z}
MAP_TILE_URL_CN = "https://mptl.#{conf.shareHostNameCn or 'realmaster.cn'}/vt/lyrs=m&x={x}&y={y}&z={z}"
MAP_TILE_URL = 'https://mptl.realmaster.com/vt/lyrs=m&x={x}&y={y}&z={z}'

#maptile url google: 'https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}'
#open street map: http://c.tile.openstreetmap.org/{z}/{x}/{y}.png

vim /etc/nginx/conf.d/mptl.conf

```
proxy_cache_path  /opt/nginx/cache  levels=1:2    keys_zone=STATIC:100m
    inactive=72h  max_size=10g;

server {
    server_name mptl.realmaster.cn mptl.realmaster.com;
    root /opt/www/mptl/public_html;
    access_log /var/log/nginx/mptl-access_log main;
    error_log /var/log/nginx/mptl-error_log warn;
    location /vt/ {
    	proxy_pass https://mt1.google.com/vt/;
        proxy_buffering        on;
        proxy_cache            STATIC;
        proxy_cache_valid      200  3d;
        proxy_cache_use_stale  error timeout invalid_header updating
                                   http_500 http_502 http_503 http_504;
    }

}
```
