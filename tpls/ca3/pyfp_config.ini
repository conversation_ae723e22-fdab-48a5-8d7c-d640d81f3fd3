[pyfpimg.imageFailure]
max_consecutive_failures_get = 5
max_consecutive_failures_save = 5
failure_window_seconds_get = 10
failure_window_seconds_save = 10

[pyfpimg.imageOutput]
floorplans_path = "/mnt/md0/floorplan/raw_b"
surveys_path = "/mnt/md0/survey/raw_b"

[pyfpimg.imageSource]
local_path_cda = "/mnt/md0/mlsimgs/crea/ddf/img/"
local_path_creb = "/mnt/md0/mlsimgs/creb/mls/"
local_path_orb = "/mnt/md0/mlsimgs/oreb/mls/"
local_path_oreb = "/mnt/md0/mlsimgs/oreb/mls/"
local_path_trb = "/mnt/md0/mlsimgs/treb/mls/"
url_host = "img.realmaster.com"

[pyfpimg.kafka.request]
bootstrap_servers = "ca1:9192,ca2:9192,ca3:9192"
topic_init = "fpimg-request-init"
topic_routine = "fpimg-request-routine"
_test_topic_init = "test-fpimg-request-init"
_test_topic_routine = "test-fpimg-request-routine"
auto_offset_reset = "earliest"
group_id = "ca3-consumer"
timeout = 60
worker_num = 6

[pyfpimg.kafka.response]
bootstrap_servers = "ca1:9192,ca2:9192,ca3:9192"
topic_name = "fpimg-response"
_test_topic_name = "test-fpimg-response"

[pyfpimg.log]
file = "./logs/pyfpimg.log"
level = "DEBUG"

[pyfpimg.model]
name = "FPC_20240621_0982_k2140.h5"
path = "model"

[pyfpimg.mongo]
host = "ca3"
port = 27037
name = "rni"
collection = "floorplan_classification"
user = "rni"
password = "RniPass"
tlsCAFile = "/etc/mongo/ca.crt"