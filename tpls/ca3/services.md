# following services are running on ca3
```bash
sh run.sh -f ./config/ca3config.ini >> logs/pyfpimg.log 2>&1 &

# TODO: use new config
./start.sh -t batch -n watchPropAndSendFloorPlanClassification_routine lib/batchBase.coffee batch/prop/watchPropAndSendFloorPlanClassification.coffee preload-model routine force
./start.sh -t batch -n floorPlanClassification lib/batchBase.coffee batch/prop/floorPlanClassification.coffee init preload-model
```