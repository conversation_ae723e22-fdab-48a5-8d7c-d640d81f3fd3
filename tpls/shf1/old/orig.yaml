appPath: /opt/rmnode/realmaster-appweb
srcPath: /opt/rmnode/realmaster-appweb/src
logPath: ./logs
cfgPath: TO-BE-SET-BY-PG
cfgBase: TO-BE-SET-BY-PG
treb:
  basedir: /mnt/mls/sync/treb/ #/opt/data/treb/mls/
crea:
  basedir: /mnt/mls/sync/crea/ddf/ #/opt/data/crea/ddf/
wwwHost: (www|w1)\.(realmaster|realexpert)\.(com|cn)
appHost: (ch\.|app\.)?(realmaster|realexpert)\.(com|cn)|192\.168\.10\.10
appHostUrl: 'https://ch.realmaster.cn'
shareHost: https://www.realmaster.cn #http://jiajia.ca
shareHostNameCn: realmaster.cn
wwwDomain: www.realmaster.cn
server:
  host: 127.0.0.1
  port: 8084
elasticSearch:
  host: https://127.0.0.1:9200
  user: 'elastic'
  password: 'a24j7xS1T11IsXZz4V4m'
  cert: '/etc/elasticsearch/certs/http_ca.crt'
mongodb:
  listing:
    host: localhost
    port: 27003
    db:   listing
    user: dbRur
    pass: 3CyzA3EfQSzGx9p9
    authdb: admin
    # keep rs format
    readPrimary: false
    tag2ReadFrom: "{dc:'china',nm:'shf3'}" # "{}"
    rs: |
      tls: true
          tlsCAFile: '/etc/mongo/ca.crt'
          tlsCertificateKeyFile: '/etc/mongo/server.pem'
          tlsAllowInvalidCertificates: true
          rsName: 'prod'
          replSet:
            rs1:
              host: 'ca3'
              port: 27013
  data:
    host: localhost
    port: 27003
    db:   data
    user: dbRur
    pass: 3CyzA3EfQSzGx9p9
    authdb: admin
    readPrimary: false
    tag2ReadFrom: "{dc:'china',nm:'shf3'}" # "{}"
    rs: |
      tls: true
          tlsCAFile: '/etc/mongo/ca.crt'
          tlsCertificateKeyFile: '/etc/mongo/server.pem'
          tlsAllowInvalidCertificates: true
          rsName: 'prod'
          replSet:
            rs1:
              host: 'ca3'
              port: 27013
    #      tag2ReadFrom: {dc:'canada',nm:'i16'}
mysql: # not realy used anymore
  host: localhost
  db:   vow
  user: vow_ru  #mysql user
  pass: VowDb123&      #mysql passwd
pgsql:
  host: localhost
  user: listing
  max:  50
  idleTimeoutMillis: 30000
  connectionTimeoutMillis: 20000
  database: 'listing'
  password: 'ezqx5j4xzx5aw77p'
  port: 5432
s3:
  bucket: f.realmaster.com
mailEngine: SES #sendGrid #'sendGrid'
devMode: false
chinaMode: true
satelliteMode: true
masterMode: false
picture:
  downloadLimit: 3000
openexchange: 98a8fbb72c1d423187674962078910d9
# update: 09041717




verbose: 1 # verbose level 0-?
# '.','./',__dirname for current dir as this file, 'cwd' for current working dir, or absolute path start with '/'.
base: '.'
srcPath: '<%this.srcPath%>'
dbs:
  verbose: 1
  mongo4: true
  chome:
    isDefault: true
    host: '<%this.mongodb.data.host%>'
    port: <%this.mongodb.data.port%>
    name: '<%this.mongodb.data.db%>'
    pool_min:1
    pool_max:1
    poolSize: 50
    user:  '<%this.mongodb.data.user%>'
    password: '<%this.mongodb.data.pass%>'
    authdb: '<%this.mongodb.data.authdb%>'
    readPrimary: <%this.mongodb.data.readPrimary%>
    tag2ReadFrom: <%this.mongodb.data.tag2ReadFrom%>
    #ssl: true
    #sslCA: './ca.crt'
    #sslCert: './client.crt'
    #sslKey: './client.key'
    #sslValidate: false
    #socketOptions:
    #  autoReconnect: true
    #  connectTimeoutMS: 180000
    #  socketTimeoutMS: 180000
    #replSet:
    #  s1:
    #    port: 27018
    #  s2:
    #    host: '127.0.0.1'
    <%this.mongodb.data.rs%>
  vow:
    host: '<%this.mongodb.listing.host%>'
    port: <%this.mongodb.listing.port%>
    name: '<%this.mongodb.listing.db%>'
    pool_min:1
    pool_max:1
    poolSize: 50
    user:  '<%this.mongodb.listing.user%>'
    password: '<%this.mongodb.listing.pass%>'
    authdb: '<%this.mongodb.listing.authdb%>'
    readPrimary: <%this.mongodb.listing.readPrimary%>
    tag2ReadFrom: <%this.mongodb.listing.tag2ReadFrom%>
    #ssl: true
    #sslCA: './ca.crt'
    #sslCert: './client.crt'
    #sslKey: './client.key'
    #sslValidate: false
    #socketOptions:
    #  autoReconnect: true
    #  connectTimeoutMS: 180000
    #  socketTimeoutMS: 180000
    #replSet:
    <%this.mongodb.listing.rs%>
i18n:
  coll: {db:'chome',coll:'i18n'}
  allowed: null
transFiles: "<%this.srcPath%>/transfiles"
abbr:
  coll: {db:'chome',coll:'abbr'}
  file: '<%this.srcPath%>/abbr.csv'
session:
  secret: 's_ec-re'
  collection: 'sess'
  #store:      'db'
  # chroot and path should not be used together.
  # For auto-reconnect functionality.
  #path: '/tmp/node_session.socket', // support: port,host parameters
  #port: 38901,
  #host: 'localhost',
  #handlers: 'sess/handler', // session server handlers definition file
  verbose: 0
  maxAge: 3600
  lasting: 3600
  store: 'sessionRedisStore'
  ignore: ['alive','sendGrid']
###
auth:
  collection: 'login'
  login:      '_id'
  password:   'passwd'
  user:       'uid'
  def_method:     'sha512'
  def_secret:     'CanBeChanged'
  user_collection: 'user'
  auth_uri:   '/auth/login'
  pass_uri:   '/'
  form:
    login:    'user'
    password: 'password'
###
vhost: '<%this.cfgPath%>/vhost.yml'
static:
  verbose: 1
  max_age: 31557600000
  cache:      true
  max_cache_size: 1024 * 512
  #site: 'webroot/site' #vhost root folder. relative or absolute path
  #site: '/site' # after chroot
  public: '<%this.srcPath%>/webroot/public'
  # publc root foler. relative to base or '/..' for absolute path
  #public: '/public' # after chroot
  #user: '<%this.srcPath%>/webroot/user' # need to set to relative path to webroot
  #store: '<%this.srcPath%>/webroot/fstore'
server:
  root: '<%this.srcPath%>/webroot' # base will be changed if this is set.
  #this is relative to orignal base too.
  noch: true
  #uid: 502
  #gid: 502
  stdout: '<%this.logPath%>/std.log'
  stderr: '<%this.logPath%>/err.log'
  port: <%this.server.port%>
  host: '<%this.server.host%>' #added lan access
  #host: '127.0.0.1'
  pid:  '<%this.logPath%>/cmate.pid'
  title: 'cmate'
proxy_header: 'x-real-ip'
log:
  console: true
  path: '<%this.logPath%>'
  mongodb:
    db:'chome'
    error: 'log_error'
    notfound: 'log_notfound'
  buffer:
    size:10
    duration:1000 #ms
  verbose:1
  sformat: ':date2 :remote-addr :method :host:url :start-time :user-agent :referrer'
  format: ':date2 :remote-addr :method :host:url :start-time :status :content-type :content-length :response-time'
  requests:
    limit: 10
    interval: 5000 # ms
    timeout: 100000 #ms developer 5s, product 100s
    level: 'warn'
multipart:
  uploadDir: '/tmp'
  keepExtensions: true
  multiples: false
production:
  error_message: "Internal Error. Please contact administrator. <EMAIL>"
lib:
  middleware:'<%this.srcPath%>/middleware'
  vendor:'<%this.srcPath%>/vendor'
themes: '<%this.srcPath%>/themes'
migration:'<%this.srcPath%>/migrate'
apps: ['<%this.srcPath%>/model','<%this.srcPath%>/apps_common','<%this.srcPath%>/apps'] # order is significently
headerfile: 'header.coffee'
importNotify:
  defaultTo: ['<EMAIL>']
  bcreTo: ['<EMAIL>','<EMAIL>']
app_config:
  devEmails:['<EMAIL>']
  azure:
    subscriptionKey: "e678a9f6c8764711849e0f17504aa696"
    endpoint: "https://api.cognitive.microsofttranslator.com"
    region: "eastus"
  deepseek:
    key: "***********************************"
    endpoint: "https://api.deepseek.com/chat/completions"
  deepL:
    key:"c8311202-a251-97d9-b15d-3795ac88e820"
    endpoint: "https://api.deepl.com/v2/translate"
  openAI:
    key:"org-XknsRihTPUr9OozG2H5byyOQ"
    endpoint: "https://api.openai.com/v1/chat/completions"
    orgID:"org-XknsRihTPUr9OozG2H5byyOQ"
    projectID:"proj_yPZHSJr6CWsj39U4jBvHfpdv"
  gemini:
    key:"AIzaSyBHoyzQTkMF_Aps9ykYZ00BRHdtfGc_TRk"
  claude:
    key:"************************************************************************************************************"
    endpoint: "https://api.anthropic.com/v1/messages"
  abTest:
    homepage: true #true是使用abtest，根据随机数分配跳转页面，false是原来的流程(setup 结束跳转登陆页)
    ratioOfA: 0.5 #A对照组比例,默认为0.5
  weatherKey:
    openweathermap: '********************************'
  weather: 'openweathermap'
  defaultEmailFromName: 'RealMaster'
  failBackEmail:['<EMAIL>']
  trustedAssignEmail: '<EMAIL>'
  projectFubEmail: '<EMAIL>'
  dualHomepage: true
  followUpBoss:
    specificEmail:'<EMAIL>'
    token:'fka_02gWVHLlA1VvNq1AR2X82ZuhXeFjtWLmjB'
    xSystem: 'realmasterWeb'
    xSystemKey: 'bade25031e36fc00f93db96e9fbc404c'
  azureSubscriptionKey: 'e678a9f6c8764711849e0f17504aa696'
  useSearchEngine: 'ES'
  elastic:
    host: '<%this.elasticSearch.host%>'
    user: '<%this.elasticSearch.user%>'
    password: '<%this.elasticSearch.password%>'
    cert: '<%this.elasticSearch.cert%>'
    verbose: 1
  here:'gI6hBaOR4eBPRKgiNE4qIUUDY6UZ-ZlAlZxZt2o5LZE'
  mapquest: '********************************'
  shareHostNameCn:'<%this.shareHostNameCn%>'
  appHostUrl: '<%this.appHostUrl%>'
  canonicalHost: 'https://www.realmaster.com'
  mapbox:
    address: 'pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A'
    # Too many request, change to use support@rm
    # web: 'pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czh6ZnExZ3NuM2pvZzB0b3dwY2xuIn0.1-ZrniVbO5fM_UzOe7Vo3Q'
    web: 'pk.eyJ1Ijoicm1zdXBwb3J0IiwiYSI6ImNrNXd0aTJqNzFjYTczam1jaGtxeGwzM3gifQ.nHvto-jVwvxc7xpTX1y7hA'
    app: 'pk.eyJ1Ijoicm1zdXBwb3J0IiwiYSI6ImNrNXd0aTJqNzFjYTczam1jaGtxeGwzM3gifQ.nHvto-jVwvxc7xpTX1y7hA'
    housemax: 'pk.eyJ1IjoiaG1heC1yZWFsbSIsImEiOiJjazY2cWdsaW4xb2N1M21vZmxidnZuNW43In0.mbrCkawOdMM--mnBnbIU8Q'
  google:
    serverAutocomplete:'AIzaSyBkc-yNjTXMkxkvnXAXrfaAcV7GaOBFFxs'
    geocoderServer:'AIzaSyDfGNii_C0VMC6ku7bWCmeDOcd0RIzzfyI' #geocoder only, TODO:need restrict ip
    browser:'AIzaSyCcnrOBfqHK-eOMGcY8tTyEe5mNc7S_WeU' #for commute:direction, place,js.TODO: change commute to mapbox,wecard
    iosAnalytics:'AIzaSyBCqhaFdE2PJWB1pPSdBy4rkRwn8TtbGGg' #restrict to app
    ios:'AIzaSyAsj6IN_2_1hPr5f_niGK3sUvViIX7omqg' #map and autocomplete etc,testkey,
    iosTest:'AIzaSyDg3M9-IWfpswsto_nXAIX9ESHt987-SvY' #restrict ios bundle, @rain, not work for ios bundle
    andriodAnalytics:'AIzaSyB3j8f64si2jzbsAxCAL5FAmb64GJS-2A8' # android only
    andriod:'AIzaSyBOslrM1NF12A_SX_SnhFXKoBxmfdeD7Gc' #map and autocomplete etc
    oldNative:'AIzaSyClYqxKKg2WdhFE9UDKA9ekFYHDIMLfdLA' #place(ios,android,sdk),map, geocoding,direction
    housemax:'AIzaSyC8WWwQzKqHPLTY5QHH82vSMM6egsJesmU'
  defaultEmail: '<EMAIL>'
  defaultRTEmail: '<EMAIL>'
  defaultReciveEmail:'<EMAIL>'
  verbose: 1
  imgServerDlAddr: 'https://f.realmaster.cn'
  imgServerUlAddr: 'https://fu.realmaster.cn'
  moveFile: 'copy'
  shareHost: '<%this.shareHost%>'
  androidNoHTTPS: false
  useHTTPS: true
  forceHTTPS: true
  appleOauth:
    client_id: "com.realmaster.web"
    team_id: "8A2DLASB37"
    key_id: "5XJFAC59B8"
    redirect_uri: "/oauth/apple"
    scope: "name email"
    client_id_app: "com.realmaster"
    authKeyPath: "<%this.cfgPath%>/../keys/appleAuthKey.p8"
  usePostgresql:false
  sqldbs:
    listingPostsql:
      verbose: 1
      tp:'pgsql'
      host: '<%this.pgsql.host%>'
      user: '<%this.pgsql.user%>'
      max: '<%this.pgsql.max%>'
      idleTimeoutMillis: '<%this.pgsql.idleTimeoutMillis%>'
      connectionTimeoutMillis: '<%this.pgsql.connectionTimeoutMillis%>'
      database: '<%this.pgsql.database%>'
      password: '<%this.pgsql.password%>'
      port: '<%this.pgsql.port%>'
  s3config:
    region:  'us-west-2' #'Oregon' #
    accessKeyId: '********************' #'********************'
    secretAccessKey: '5auap7yre6y1GaMegfq9cYCSq9uEDOnkCzeXjeUH' #'xcw9aw1rzbujrzImJLr/MpUPZyFREZuUsdkXTvOu'
    bucket: '<%this.s3.bucket%>'
  user_file:
    wwwbase: 'f.realmaster.cn'
    folder: 'P'
    protocol: 'https'
    disableUpload: false
  media:
    wwwbase: 'http://realmaster.com'
    path: '<%this.srcPath%>/webroot/public'
  template:
    path: '<%this.srcPath%>/shareImgTemplate'
  emailTemplate:
    path: '<%this.srcPath%>/emailTemplate'
  eziIPs: ['']#['************','************'] #['***********','***********']
  isharingIPs: ['**************','***************']
  wechat: 'RealMaster'
  wechatAppId: 'wx486c08aa3a05089d' #'wxf43abc63fd6c1b19'
  wechatSecret: '2b37e59a16594a8faebaa509a0da9956' #'fcd6d3800bb2906bacbee676685a0de4'
  wechatDomain: 'realmaster.cn' #'realmaster.cn'
  wechatWeb:
    AppID: 'wx486c08aa3a05089d' #'wxf43abc63fd6c1b19' #'wxd1989971aaef81e7'
    AppSecret: '2b37e59a16594a8faebaa509a0da9956' #'fcd6d3800bb2906bacbee676685a0de4' #'0e3783110749989da5c93b170d04609a'
  wechatApp:
    AppID: 'wxcf33ce325754da25'
    AppSecret: '69a59252b9f587356b70706d4080d2ae'
    domain: 'https://realmaster.com/'
  iShare:
    secret: 'ojF7llUbqMJTHxQLr75a6IrPtsnR6DyI'
    wwwbase: 'http://www.isharing.co/api/realmaster'
  share:
    secret: '547dc488a8d7d5828d34437872064a9b'
  wwwDomain: '<%this.wwwDomain%>'
  openexchangeratesKey: '<%this.openexchange%>' #'********************************' # <EMAIL>
  openexchangeratesInterval: 4
  wbcid: '39141494639361'
  wbcsec: 'xMPUNwIZFJrvTK08t0ak'
  geetestId: '9407943ac70054a2f029b6bd3dafbd27' #'32eb89e91d23af79b864ddea2a4a5361'
  geetestKey: '438cc575fff662a7564e29945ccb75b9' #'5d02237baf1cefb6b2b0b091a88a90d4'
  recaptchaSiteKey: '6LeMsw8UAAAAAGKIonO4ImluXmOxwXKrT9tnl0PO' #'6LfSgw4UAAAAAAel6FMchUgPJoO3vv_C4BUG1lDV'
  recaptchaSecretKey: '6LeMsw8UAAAAAGRLxWFVflvVXu4g53B-NZt4M6Iv' #'6LfSgw4UAAAAANsKMeOTHcxXHs3HXmbFQZZAwY1j'
  stripePubKey: 'pk_live_QYqyuEDiIXBy6R39TBbzmAin'
  stripeSecret: '********************************'
  wpBackEndDomain: 'https://www.realmaster.com/wpheaderfooter/'
mailEngineList:[
  {
    engine: 'SES'
    options:
      accessKeyId: '********************'
      secretAccessKey: 'NqYJDdVhIMdvVqlJnmBSIBsbHKCEQLlkw91/v4rA'
      region: 'us-east-2'
      email: '<EMAIL>'
      #url: 'http://9*************:8088/ses.php1'
  }
  {
    engine: 'rmMail'
    options:
      defaultEmail: '<EMAIL>'
      url: 'https://ml1.realmaster.cc/send'
  }
]
mail:
  smtp:
    service: "Gmail"
    auth:
      user:"<EMAIL>"
      pass:"RealMasterCOM***"
ses:
  accessKeyId: '********************'
  defaultEmail: '<EMAIL>'
  secretAccessKey: 'Uogp6eMqmbIhxB43mqCYPJYBT9x3vnQZwrawvz6W'
  region: 'us-east-1'
  url:'http://*************:8088/ses.php'
sendmail:
  defaultEmail: '<EMAIL>'
rmMail:
  defaultEmail: '<EMAIL>'
  url: 'https://ml1.realmaster.cc/send'
gmail:
  defaultEmail: '<EMAIL>'
sendGrid:
  apiKey: '*********************************************************************'
  fromEmail: '<EMAIL>'
  wpBackEndDomain: 'http://www.realmaster.cn/wpheaderfooter/'
mailEngine: '<%this.mailEngine%>'
mailEngine2: 'rmMail'
twilio:
  sid: '**********************************'
  authToken: '5ccb19e204f6cfa14dd333c1db9649e8'
#wx_config:
#  RealMaster:
#    appid: 'wxf43abc63fd6c1b19'
#    secret: 'fcd6d3800bb2906bacbee676685a0de4'
geoCoderService:
  token: 'aUIh0DMLQY'
  url: 'https://www.realmaster.com/geocodingService'
  useRemoteService: true
  allowedIPs: [] # if it's empty array or undefined, ignore ip restriction
apn_config:
  cert: '<%this.cfgPath%>/../keys/rain20251031cert.pem'
  key: '<%this.cfgPath%>/../keys/rain20251031key.pem'
  token:
    key: "<%this.cfgPath%>/../keys/AuthKey_R8CP2B87H9.p8"
    keyId: "R8CP2B87H9"
    teamId: "3ZJZ7RQ3Q9"
  production: true
  passphrase: null
  topic: "com.realmaster"
gcm_config:
  key: 'AIzaSyDoqAESaLRAnXrfo4WR5iALPFU80D0uguw'
fcmv1_config:
  json: '<%this.cfgPath%>/../keys/service-account.json'
fcm_config:
  key: 'AAAAUBisLlk:APA91bHaz_PgjFcpxoIwuyTqikRqEmkprUuAyMu0BIrJYEdl3lU2M9tITF6783KZvQgqoJvlnVcERlJ5PMVtG_cBt2fQz5ivRvhxXL-DeSzTzqCmfkH_QqiDVkl29LYF6KC3hseB7Hh6'
socket:
  path: '/socket'
p58:
  protocol: 'http'
  host: 'autoinfo.gm.58.com' #'autoinfo.58v5.cn'
developer_mode: <%this.devMode%> #for product
chinaMode: <%this.chinaMode%>
satelliteMode: <%this.satelliteMode%>
masterMode: <%this.masterMode%>
preDefColls:
  uuid:
    dbName:'chome'
    collName:'user_uuid'
  mailLog:
    dbName:'chome'
    collName:'mail_log'
    options:
      timeseries:
        timeField:'timestamp'
        metaField:'metadata'
      expireAfterSeconds:30*24*3600
  emailWhitelist:
    dbName:'chome'
    collName:'email_whitelist'
  emailMXFailed:
    dbName:'chome'
    collName:'email_mx_failed'

preDefBatchColls:
  mailLog:
    dbName:'chome'
    collName:'mail_log'
    options:
      timeseries:
        timeField:'timestamp'
        metaField:'metadata'
      expireAfterSeconds:30*24*3600
ofe: false
geoip: false
profiler:
  freq: 60000
  file: '<%this.logPath%>/profile.log'
limitAccessRate:
  AccessPerSessionLimit: 300
  SessionPerIPThreshold: 20
  AvgAccessPerSessionIP: 2