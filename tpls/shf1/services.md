# following services are running on shf3a
```bash
systemctl --user status appweb
systemctl --user list-units --type=service --no-pager

./start.sh -t batch -n watchPropAndUpdateElasticSearch -cmd "lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee routine force"
systemctl status --user <EMAIL>
```

# dirs
log_dir: /home/<USER>/rmprodlogs
src_dir: /opt/rmappweb/src
cfg_dir: /home/<USER>/rmprodconfig


# helpful cmds
```
wd cfg_new
wd src_new
wd logs_new
```
