[contact]
trustedAssignEmail = '<EMAIL>'
alertSMS = '01234556'
alertEmail = '<EMAIL>'
defaultReciveEmail =''
projectFubEmail = '<EMAIL>'

[importNotify.bcreTo]
0 = "<EMAIL>"

[importNotify.defaultTo]
0 = "<EMAIL>"

[dbs]
verbose = 1

[dbs.chome]
uri="**********************************************************************************************************************************************************************************************************************************************************************************************************************************************"

[dbs.rni]
uri="************************************************************************************************************************************************************************************************************************************************"


[dbs.tmp]
uri="************************************************************************************************************************************************************************************************************************************************"


[dbs.vow]
uri="*************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

[elastic]
cert = "/etc/elasticsearch/certs/http_ca.crt"
host = "https://localhost:9200"
password = "E=0*4p9URuWf-jqZpg1b"
verbose = 2

[mailEngine]
mailEngine = "mockmail"

[mailEngine.gmail]
defaultEmail = ""

[mailEngine.gmail.auth]
pass = "dummy"
user = "<EMAIL>"

[mailEngine.mockMail]
mock = false
verbose = 3

[mailEngine.sendmail]
defaultEmail = ""

[mailEngine.sesH]
defaultEmail = ""

[mailEngine.sesL]
defaultEmail = ""

[user_file]
wwwbase = "fs_t.realmaster.c"

[propertiesBase]
useSearchEngine = "Mongo"

[limitAccessRate]
AccessPerSessionLimit = 9_900
AvgAccessPerSessionIP = 920
SessionPerIPThreshold = 9100

[server]
port = 8_099

[serverBase]
appHostUrl=''
canonicalHost=''
verbose = 1
wwwDomain = "www.test:8080"
srcPath = "../realmaster-appweb/src"

[static]
verbose = 2

[share]
host = "http://www.test:8080"
hostNameCn = "realexpert.cn"
secret = "547dc488a8d7d5828d34437872064a9b"

[azure]
subscriptionKey = "e678a9f6c8764711849e0f17504aa696"