### before running test, you need to run start.sh and make sure the server can run without error

```
./start.sh -t unit_test -e <EMAIL>
./start.sh -t unit_testES -e <EMAIL>
```

# start.sh invokes testPassed.sh
# 不能直接跑./unitTest/test.sh -f， 没有unit_test.ini， 需要先跑./start.sh -t unit_test
# 不能配置了local.test.ini后直接跑test，需要配置local.ini
# 不能配置了local.testEs.ini后直接跑test，需要配置local.test.ini
```
./unitTest/testPassed.sh -e <EMAIL> -d ./
./unitTest/test.sh -f libapp/saveToMaster.js
./unitTest/test.sh -f elasticsearch/elasticSearch.js
./unitTest/test.sh -m propertyImport

systemctl --user stop unit_test.service
```