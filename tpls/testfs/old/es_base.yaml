
#base.yaml
pyPath: ../rmpython
pyActive: "source $HOME/environment/penv/bin/activate"
appPath: ../realmaster-appweb
srcPath: <%this.appPath%>/src
logPath: ./logs
cfgPath: TO-BE-SET-BY-PG
cfgBase: TO-BE-SET-BY-PG
treb:
  basedir: /opt/data/treb/mls/
crea:
  basedir: /opt/data/crea/ddf/
wwwHost: www.test
appHost: app.test
logLevel: 1
shareHost: www.test:8080
wwwDomain: www.test:8080
server:
  host: 127.0.0.1
  port:  8098
elasticSearch:
  host: https://127.0.0.1:9201
  user: 'elastic'
  password: 'KKLZgCHvJV0Yls9wSY8L'
  cert: '/opt/testfs/elastic/elasticsearch-8.12.2/config/certs/http_ca.crt'
mongodb:
  listing:
    host: ca12 #localhost
    port: 27017
    db:   listingTest #your local db name for listing
    user: d1t # db user name
    pass: d1t # db pwd
    authdb: admin
    #rsName: 'rs0'
    readPrimary: false
    tag2ReadFrom: "{dc:'canada',nm:'d1'}" # "{}"
    rs: |
      tls: true
          tlsCAFile: '/etc/mongo/ca.crt'
          tlsCertificateKeyFile: '/etc/mongo/client/ca12.pem' #'/etc/mongo/server.pem'
          tlsAllowInvalidCertificates: true
          rsName: 'dev'
          replSet:
            rs1:
              host: 'ca12a'
              port: 27018
    # rs: |
    #   rsName: 'dev'
    #       replSet:
    #         rs1:
    #           host: 'localhost'
    #           port: 27017
    #         rs2:
    #           host: 'localhost'
    #           port: 27019
  data:
    host: ca12 #localhost
    port: 27017
    db:   dataTest # your local db name for data
    user: d1t #db user name
    pass: d1t #db pwd
    authdb: admin
    readPrimary: false
    #rsName: 'rs0'
    tag2ReadFrom: "{dc:'canada',nm:'d1'}" # "{}"
    rs: |
      tls: true
          tlsCAFile: '/etc/mongo/ca.crt'
          tlsCertificateKeyFile: '/etc/mongo/client/ca12.pem' #'/etc/mongo/server.pem'
          tlsAllowInvalidCertificates: true
          rsName: 'dev'
          replSet:
            rs1:
              host: 'ca12a'
              port: 27018
    # rs: |
    #   rsName: 'dev'
    #       replSet:
    #         rs1:
    #           host: 'localhost'
    #           port: 27017
    #         rs2:
    #           host: 'localhost'
    #           port: 27019
  rni: # save log database
    host: ca12 #localhost #************
    port: 27017
    db:   rniTest
    authdb: admin
    readPrimary: false
    tag2ReadFrom: "{}" #"{dc:'canada',nm:'i16'}"
    rsName: 'rs0'
    user: rnit
    pass: rnit
mysql:
#  host: *************
  # host: localhost
  port: 3303
  # port: 3306
  db:   vow
  user: rootu5
  pass: rootu5
  # user: vow_u
  # pass: vow_u
pgsql:
  #host: '*************'
  #host: '*************'
  host: 'localhost'
  user: 'listing'
  max: 12
  idleTimeoutMillis: 10
  connectionTimeoutMillis: 20000
  database: 'listing'
  password: 'listing'
  port: 5412
  pool: 10
s3:
  bucket: f.i.realmaster.com
mailEngine: mockmail #sendGrid
alertEmail: '<EMAIL>' #alert will be send to this eamil
alertSMS: '+16476337771' #sms alert will be send to this phone#
mailEngine2: rmMail
defaultSenderEmail: '<EMAIL>' #defaultEmail in config.coffee
defaultRTEmail: '<EMAIL>' # *(required) default reply to email when user want to reply
stripePubKey:
stripeSecret:
devMode: true
chinaMode: false #false
satelliteMode: false
masterMode: true
openexchange: 65b664378f724316abc6220ccec093fd # jiajiator
picture:
  downloadLimit: 2000

# tpls/config.coffee
verbose: 1 # verbose level 0-?
# '.','./',__dirname for current dir as this file, 'cwd' for current working dir, or absolute path start with '/'.
base: '.'
dbs:
  # v36: true
  verbose: 1
  mongo4: true
  chome:
    isDefault: true
    host: '<%this.mongodb.data.host%>'
    port: <%this.mongodb.data.port%>
    name: '<%this.mongodb.data.db%>'
    pool_min:1
    pool_max:10
    user:  '<%this.mongodb.data.user%>'
    password: '<%this.mongodb.data.pass%>'
    authdb: '<%this.mongodb.data.authdb%>'
    readPrimary: <%this.mongodb.data.readPrimary%>
    tag2ReadFrom: <%this.mongodb.data.tag2ReadFrom%>
    # ssl: true
    # sslCA: '/etc/ssl/mongodb.pem'
    # sslCert: '/etc/ssl/client-cert.crt'
    # sslKey: '/etc/ssl/client-cert.key'
    # sslValidate: false
    socketOptions:
      #autoReconnect: true
      connectTimeoutMS: 180000
      socketTimeoutMS: 180000
    rsName:'<%this.mongodb.data.rsName%>'
    #  s1:
    #    port: 27018
    #  s2:
    #    host: '127.0.0.1'
    <%this.mongodb.data.rs%>
  vow:
    host: '<%this.mongodb.listing.host%>'
    port: <%this.mongodb.listing.port%>
    name: '<%this.mongodb.listing.db%>'
    pool_min:1
    pool_max:10
    user:  '<%this.mongodb.listing.user%>'
    password: '<%this.mongodb.listing.pass%>'
    authdb: '<%this.mongodb.listing.authdb%>'
    readPrimary: <%this.mongodb.listing.readPrimary%>
    tag2ReadFrom: <%this.mongodb.listing.tag2ReadFrom%>
    # ssl: true
    # sslCA: '/etc/ssl/mongodb.pem'
    # sslCert: '/etc/ssl/client-cert.crt'
    # sslKey: '/etc/ssl/client-cert.key'
    # sslValidate: false
    socketOptions:
      #autoReconnect: true
      connectTimeoutMS: 180000
      socketTimeoutMS: 180000
    #replSet:
    rsName:'<%this.mongodb.listing.rsName%>'
    <%this.mongodb.listing.rs%>
  rni:
    host: '<%this.mongodb.rni.host%>'
    port: <%this.mongodb.rni.port%>
    name: '<%this.mongodb.rni.db%>'
    pool_min:1
    pool_max:10
    user:  '<%this.mongodb.rni.user%>'
    password: '<%this.mongodb.rni.pass%>'
    authdb: '<%this.mongodb.rni.authdb%>'
    readPrimary: <%this.mongodb.rni.readPrimary%>
    tag2ReadFrom: <%this.mongodb.rni.tag2ReadFrom%>
    tls: true
    tlsCAFile: '/etc/mongo/ca.crt'
    tlsCertificateKeyFile: '/etc/mongo/client/ca12.pem'
    # ssl: true
    # sslCA: '/etc/ssl/mongodb.pem'
    # sslCert: '/etc/ssl/client-cert.crt'
    # sslKey: '/etc/ssl/client-cert.key'
    # sslValidate: false
    socketOptions:
      autoReconnect: true
      connectTimeoutMS: 180000
      socketTimeoutMS: 180000
    #replSet:
    rsName:'<%this.mongodb.rni.rsName%>'
    <%this.mongodb.rni.rs%>
i18n:
  coll: {db:'chome',coll:'i18n'}
  allowed: null
transFiles: "<%this.srcPath%>/transfiles"
abbr:
  coll: {db:'chome',coll:'abbr'}
  file: '<%this.srcPath%>/abbr.csv'
session:
  secret: 's_ec-re'
  collection: 'sess'
  #store:      'db'
  # chroot and path should not be used together.
  # For auto-reconnect functionality.
  #path: '/tmp/node_session.socket', // support: port,host parameters
  #port: 38901,
  #host: 'localhost',
  #handlers: 'sess/handler', // session server handlers definition file
  verbose: 0
  maxAge: 3600
  lasting: 3600
  store: 'sessionRedisStore'
  name: 'App'
  namespace: 'Rm:'
  ignore: ['alive','sendGrid']
###
auth:
  collection: 'login'
  login:      '_id'
  password:   'passwd'
  user:       'uid'
  def_method:     'sha512'
  def_secret:     'CanBeChanged'
  user_collection: 'user'
  auth_uri:   '/auth/login'
  pass_uri:   '/'
  form:
    login:    'user'
    password: 'password'
###
vhost: '<%this.cfgPath%>/vhost.yml'
static:
  verbose: 2
  max_age: 31557600000
  cache:      true
  max_cache_size: 1024 * 512
  #site: 'webroot/site' #vhost root folder. relative or absolute path
  #site: '/site' # after chroot
  public: '<%this.srcPath%>/webroot/public'
  # publc root foler. relative to base or '/..' for absolute path
  #public: '/public' # after chroot
  #user: '<%this.srcPath%>/webroot/user' # need to set to relative path to webroot
  #store: '<%this.srcPath%>/webroot/fstore'
server:
  root: '<%this.srcPath%>/webroot' # base will be changed if this is set.
  #this is relative to orignal base too.
  noch: true
  #uid: 502
  #gid: 502
  stdout: '<%this.logPath%>/std.log'
  stderr: '<%this.logPath%>/err.log'
  port: <%this.server.port%>
  host: '<%this.server.host%>' #added lan access
  #host: '127.0.0.1'
  pid:  '<%this.logPath%>/cmate.pid'
  title: 'cmate'
proxy_header: 'x-real-ip'
log:
  console: true
  path: '<%this.logPath%>'
  mongodb:
    db:'chome'
    error: 'log_error'
    notfound: 'log_notfound'
  buffer:
    size:10
    duration:1000 #ms
  verbose:1
  sformat: ':date2 :remote-addr :method :host:url :start-time :user-agent :referrer'
  format: ':date2 :remote-addr :method :host:url :start-time :status :content-type :content-length :response-time'
  requests:
    limit: 10
    interval: 5000 # ms
    timeout: 100000 #ms developer 5s, product 100s
    level: 'warn'
multipart:
  uploadDir: '/tmp'
  keepExtensions: true
  multiples: false
production:
  error_message: "Internal Error. Please contact administrator. <EMAIL>"
lib:
  middleware:'<%this.srcPath%>/middleware'
  vendor:'<%this.srcPath%>/vendor'
themes: '<%this.srcPath%>/themes'
themesAutoReload: true
migration:'<%this.srcPath%>/migrate'
#apps: ['<%this.srcPath%>/appsTest'] # order is significently
apps: ['<%this.srcPath%>/model','<%this.srcPath%>/apps_common','<%this.srcPath%>/apps'] # order is significently
#exclude_apps: ['WebRM']
headerfile: 'header.coffee'
unit_test:
  cleanTestDB:true
app_config:
  skipSendLeadToCrm: true
  alertSMS: '<%this.alertSMS%>'
  alertEmail: '<%this.alertEmail%>'
  noSitemap: 1
  useSearchEngine: 'Mongo'
  followUpBoss:
    token:'xxfka_02gWVHLlA1VvNq1AR2X82ZuhXeFjtWLmjB'
    xSystem: 'realmasterWeb'
    xSystemKey: 'xxbade25031e36fc00f93db96e9fbc404c'
  elastic:
    host: '<%this.elasticSearch.host%>'
    user: '<%this.elasticSearch.user%>'
    password: '<%this.elasticSearch.password%>'
    cert: '<%this.elasticSearch.cert%>'
    verbose: 2
  defaultEmail: '<%this.defaultSenderEmail%>'
  defaultRTEmail: '<%this.defaultRTEmail%>'
  trebManualImport:
    # relayDomain:'http://d9.realmaster.com'
    skipManualImportAuth:true
  propTagFilePath: '<%this.srcPath%>'
  wpBackEndDomain:'https://d6w.realmaster.cn/'
  verbose: 1
  kafka:
    clientId: 'batch-server'
    brokers: ['localhost:9092']
    verbose: 3
    consumerGroupId: 'batch-server'
    topic:
      condosToGetSqft: 'req.sqft.condos.local'
      sqftFromCondosCa: 'res.sqft.condosCa.local'
  shareHost: 'http://<%this.shareHost%>'
  useMysql:false
  usePostgresql:false
  mapbox:
    web: 'pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A'
    app: 'pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A'
    housemax: 'pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A'
  google:
    geocoderServer:'AIzaSyDfGNii_C0VMC6ku7bWCmeDOcd0RIzzfyI' #server geocoder only, TODO:need restrict ip
    browser:'AIzaSyCcnrOBfqHK-eOMGcY8tTyEe5mNc7S_WeU' #for commute:direction, place,js.TODO: change commute to mapbox,wecard
    iosAnalytics:'AIzaSyBCqhaFdE2PJWB1pPSdBy4rkRwn8TtbGGg' #native, restrict to app
    ios:'AIzaSyAsj6IN_2_1hPr5f_niGK3sUvViIX7omqg' #ios native google map,
    x_iosTest:'AIzaSyDg3M9-IWfpswsto_nXAIX9ESHt987-SvY' #restrict ios bundle, TODO:@rain, test if working
    andriodAnalytics:'AIzaSyB3j8f64si2jzbsAxCAL5FAmb64GJS-2A8' # android only
    andriod:'AIzaSyBOslrM1NF12A_SX_SnhFXKoBxmfdeD7Gc' #android native map
    oldNative:'AIzaSyClYqxKKg2WdhFE9UDKA9ekFYHDIMLfdLA' #place(ios,android,sdk),map, geocoding,direction, !deprecated, will be removed in new versions
    housemax:'AIzaSyC8WWwQzKqHPLTY5QHH82vSMM6egsJesmU' #3rd paty
    serverAutocomplete:'AIzaSyBkc-yNjTXMkxkvnXAXrfaAcV7GaOBFFxs' #server autocomplete key(in new version)
  sqldbs:
    listingPostsql:
      verbose: 2
      dbNm:'listingPostsql'
      tp:'pgsql'
      host: '<%this.pgsql.host%>'
      user: '<%this.pgsql.user%>'
      max: '<%this.pgsql.max%>'
      idleTimeoutMillis: '<%this.pgsql.idleTimeoutMillis%>'
      connectionTimeoutMillis: '<%this.pgsql.connectionTimeoutMillis%>'
      database: '<%this.pgsql.database%>'
      password: '<%this.pgsql.password%>'
      port: '<%this.pgsql.port%>'
  #   vow:
  #     type:'mysql'
  #     database: '<%this.mysql.db%>'
  #     user: '<%this.mysql.user%>'
  #     password: '<%this.mysql.pass%>'
  #     host: '<%this.mysql.host%>'
  #     insecureAuth:true
  #     port:'<%this.mysql.port%>'
  s3config:
    region:  'us-west-2' #'Oregon' #
    accessKeyId: '********************' #'********************'
    secretAccessKey: '5auap7yre6y1GaMegfq9cYCSq9uEDOnkCzeXjeUH' #'xcw9aw1rzbujrzImJLr/MpUPZyFREZuUsdkXTvOu'
    bucket: '<%this.s3.bucket%>'
  user_file:
    wwwbase: 'fs_t.realmaster.cn'
    folder: 'G'
    protocol: 'http'
    disableUpload: false
  publicImgFilePath: '<%this.srcPath%>/webroot/public/img' #'<%this.srcPath%>/webroot/public/img'
  publicImgErrorFile: '<%this.srcPath%>/s3uploadError.log'
  imgServerUlAddr: 'https://fu_t.realmaster.cn'
  imgServerDlAddr: 'http://f.realmaster.cn/'
  media:
    wwwbase: 'http://realmaster.com'
    path: '<%this.srcPath%>/webroot/public'
  template:
    path: '<%this.srcPath%>/shareImgTemplate'
  emailTemplate:
    path: '<%this.srcPath%>/emailTemplate'
  eziIPs: ['']#['************','************'] #['***********','***********']
  isharingIPs: ['**************','***************']
  wechat: 'RealMaster'
  wechatAppId: 'wxf43abc63fd6c1b19'
  wechatSecret: 'fcd6d3800bb2906bacbee676685a0de4'
  wechatDomain: 'realmaster.cn'
  wechatWeb:
    AppID: 'wxd1989971aaef81e7'
    AppSecret: '0e3783110749989da5c93b170d04609a'
  wechatApp:
    AppID: 'wxcf33ce325754da25'
    AppSecret: '69a59252b9f587356b70706d4080d2ae'
  iShare:
    secret: 'ojF7llUbqMJTHxQLr75a6IrPtsnR6DyI'
    wwwbase: 'http://www.isharing.co/api/realmaster'
  share:
    secret: '547dc488a8d7d5828d34437872064a9b'
  wwwDomain: '<%this.wwwDomain%>'
  openexchangeratesKey: '<%this.openexchange%>' #'********************************' # <EMAIL>
  openexchangeratesInterval: 48
  wbcid: '39141494639361'
  wbcsec: 'xMPUNwIZFJrvTK08t0ak'
  geetestId: '9407943ac70054a2f029b6bd3dafbd27' #'32eb89e91d23af79b864ddea2a4a5361'
  geetestKey: '438cc575fff662a7564e29945ccb75b9' #'5d02237baf1cefb6b2b0b091a88a90d4'
  recaptchaSiteKey: '6LeMsw8UAAAAAGKIonO4ImluXmOxwXKrT9tnl0PO' #'6LfSgw4UAAAAAAel6FMchUgPJoO3vv_C4BUG1lDV'
  recaptchaSecretKey: '6LeMsw8UAAAAAGRLxWFVflvVXu4g53B-NZt4M6Iv' #'6LfSgw4UAAAAANsKMeOTHcxXHs3HXmbFQZZAwY1j'
  stripePubKey: 'pk_test_xuuOG1SkPt8l46NikwVbp39m'
  stripeSecret: 'sk_test_0cMa8JlEYGFI51Pv3Yn9cHdR'
  wordpressHost: 'http://**************:8080'
  wordpressJWT:'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************.hcrjbJnmZe6F9VjNiwL6aFPLcOp1sAIClR3r4UTbrWM'
  here:'gI6hBaOR4eBPRKgiNE4qIUUDY6UZ-ZlAlZxZt2o5LZE',
  mapquest: '********************************'
mail:
  smtp:
    # NOTE: no longer used, not useable
    service: "Gmail"
    auth:
      user: '<EMAIL>'
      pass: 'dummy'
      # user:"<EMAIL>"
      # pass:"RealMasterCOM***"
# TODO: use new local mail engine
ses:
  accessKeyId: '********************'
  secretAccessKey: 'Uogp6eMqmbIhxB43mqCYPJYBT9x3vnQZwrawvz6W'
  region: 'us-east-1'
  defaultEmail: '<EMAIL>'
  url:'http://*************:8088/ses.php'
sendGrid:
  apiKey: '*********************************************************************'
  fromEmail: '<EMAIL>'
mailEngine: '<%this.mailEngine%>'
twilio:
  sid: '**********************************'
  authToken: '5ccb19e204f6cfa14dd333c1db9649e8'
wx_config:
  RealMaster:
    appid: 'wxf43abc63fd6c1b19'
    secret: 'fcd6d3800bb2906bacbee676685a0de4'
apn_config:
#  cert: '<%this.cfgPath%>/../keys/aps_production.pem'
#  key: '<%this.cfgPath%>/../keys/key.pem'
#  token:
#    key: "<%this.cfgPath%>/../keys/AuthKey_R8CP2B87H9.p8"
#    keyId: "R8CP2B87H9"
#    teamId: "3ZJZ7RQ3Q9"
  production: false
  passphrase: null
  topic: "com.realmaster"
gcm_config:
  key: 'AIzaSyDoqAESaLRAnXrfo4WR5iALPFU80D0uguw'
fcm_config:
  key: 'AAAAUBisLlk:APA91bHaz_PgjFcpxoIwuyTqikRqEmkprUuAyMu0BIrJYEdl3lU2M9tITF6783KZvQgqoJvlnVcERlJ5PMVtG_cBt2fQz5ivRvhxXL-DeSzTzqCmfkH_QqiDVkl29LYF6KC3hseB7Hh6'
socket:
  path: '/socket'
p58:
  protocol: 'http'
  host: 'autoinfo.gm.58.com' #'autoinfo.58v5.cn'
developer_mode: <%this.devMode%> #for product
logLevel:<%this.logLevel%>
chinaMode: <%this.chinaMode%>
satelliteMode: <%this.satelliteMode%>
masterMode: <%this.masterMode%>
ofe: false
profiler:
  freq: 60000
  file: '<%this.logPath%>/profile.log'
limitAccessRate:
  AccessPerSessionLimit: 900
  SessionPerIPThreshold: 100
  AvgAccessPerSessionIP: 20

# tpls/config4.coffee
verbose: 3 # verbose level 0-?
# '.','./',__dirname for current dir as this file, 'cwd' for current working dir, or absolute path start with '/'.
base: '.'
dbs:
  # v36: true
  mongo4: true
  verbose: 1
  chome:
    isDefault: true
    host: '<%this.mongodb.data.host%>'
    port: <%this.mongodb.data.port%>
    name: '<%this.mongodb.data.db%>'
    pool_min:1
    pool_max:10
    user:  '<%this.mongodb.data.user%>'
    password: '<%this.mongodb.data.pass%>'
    authdb: '<%this.mongodb.data.authdb%>'
    readPrimary: <%this.mongodb.data.readPrimary%>
    tag2ReadFrom: <%this.mongodb.data.tag2ReadFrom%>
    # ssl: true
    # sslCA: '/etc/ssl/mongodb.pem'
    # sslCert: '/etc/ssl/client-cert.crt'
    # sslKey: '/etc/ssl/client-cert.key'
    tls: true
    tlsCAFile: '/etc/mongo/ca.crt'
    tlsCertificateKeyFile: '/etc/mongo/client/ca12.pem'
    # sslValidate: false
    socketOptions:
      autoReconnect: true
      connectTimeoutMS: 180000
      socketTimeoutMS: 180000
    rsName:'<%this.mongodb.data.rsName%>'
    #  s1:
    #    port: 27018
    #  s2:
    #    host: '127.0.0.1'
    <%this.mongodb.data.rs%>
  vow:
    host: '<%this.mongodb.listing.host%>'
    port: <%this.mongodb.listing.port%>
    name: '<%this.mongodb.listing.db%>'
    pool_min:1
    pool_max:10
    user:  '<%this.mongodb.listing.user%>'
    password: '<%this.mongodb.listing.pass%>'
    authdb: '<%this.mongodb.listing.authdb%>'
    readPrimary: <%this.mongodb.listing.readPrimary%>
    tag2ReadFrom: <%this.mongodb.listing.tag2ReadFrom%>
    tls: true
    tlsCAFile: '/etc/mongo/ca.crt'
    tlsCertificateKeyFile: '/etc/mongo/client/ca12.pem'
    # ssl: true
    # sslCA: '/etc/ssl/mongodb.pem'
    # sslCert: '/etc/ssl/client-cert.crt'
    # sslKey: '/etc/ssl/client-cert.key'
    # sslValidate: false
    socketOptions:
      autoReconnect: true
      connectTimeoutMS: 180000
      socketTimeoutMS: 180000
    #replSet:
    rsName:'<%this.mongodb.listing.rsName%>'
    <%this.mongodb.listing.rs%>
  rni:
    host: '<%this.mongodb.rni.host%>'
    port: <%this.mongodb.rni.port%>
    name: '<%this.mongodb.rni.db%>'
    pool_min:1
    pool_max:10
    user:  '<%this.mongodb.rni.user%>'
    password: '<%this.mongodb.rni.pass%>'
    authdb: '<%this.mongodb.rni.authdb%>'
    readPrimary: <%this.mongodb.rni.readPrimary%>
    tag2ReadFrom: <%this.mongodb.rni.tag2ReadFrom%>
    tls: true
    tlsCAFile: '/etc/mongo/ca.crt'
    tlsCertificateKeyFile: '/etc/mongo/client/ca12.pem'
    # ssl: true
    # sslCA: '/etc/ssl/mongodb.pem'
    # sslCert: '/etc/ssl/client-cert.crt'
    # sslKey: '/etc/ssl/client-cert.key'
    # sslValidate: false
    socketOptions:
      #autoReconnect: true
      connectTimeoutMS: 180000
      socketTimeoutMS: 180000
    #replSet:
    rsName:'<%this.mongodb.rni.rsName%>'
    <%this.mongodb.rni.rs%>
  tmp:
    host: '<%this.mongodb.rni.host%>'
    port: <%this.mongodb.rni.port%>
    name: '<%this.mongodb.rni.db%>'
    pool_min:1
    pool_max:10
    user:  '<%this.mongodb.rni.user%>'
    password: '<%this.mongodb.rni.pass%>'
    authdb: '<%this.mongodb.rni.authdb%>'
    readPrimary: <%this.mongodb.rni.readPrimary%>
    tag2ReadFrom: <%this.mongodb.rni.tag2ReadFrom%>
    tls: true
    tlsCAFile: '/etc/mongo/ca.crt'
    tlsCertificateKeyFile: '/etc/mongo/client/ca12.pem'
    # ssl: true
    # sslCA: '/etc/ssl/mongodb.pem'
    # sslCert: '/etc/ssl/client-cert.crt'
    # sslKey: '/etc/ssl/client-cert.key'
    # sslValidate: false
    socketOptions:
      #autoReconnect: true
      connectTimeoutMS: 180000
      socketTimeoutMS: 180000
    #replSet:
    rsName:'<%this.mongodb.rni.rsName%>'
    <%this.mongodb.rni.rs%>
i18n:
  coll: {db:'chome',coll:'i18n'}
  allowed: null
transFiles: "<%this.srcPath%>/transfiles"
abbr:
  coll: {db:'chome',coll:'abbr'}
  file: '<%this.srcPath%>/abbr.csv'
session:
  secret: 's_ec-re'
  collection: 'sess'
  #store:      'db'
  # chroot and path should not be used together.
  # For auto-reconnect functionality.
  #path: '/tmp/node_session.socket', // support: port,host parameters
  #port: 38901,
  #host: 'localhost',
  #handlers: 'sess/handler', // session server handlers definition file
  verbose: 0
  maxAge: 3600
  lasting: 3600
  store: 'sessionRedisStore'
  name: 'App'
  namespace: 'Rm:'
  ignore: ['alive','sendGrid']
###
auth:
  collection: 'login'
  login:      '_id'
  password:   'passwd'
  user:       'uid'
  def_method:     'sha512'
  def_secret:     'CanBeChanged'
  user_collection: 'user'
  auth_uri:   '/auth/login'
  pass_uri:   '/'
  form:
    login:    'user'
    password: 'password'
###
vhost: '<%this.cfgPath%>/vhost.yml'
static:
  verbose: 2
  max_age: 31557600000
  cache:      true
  max_cache_size: 1024 * 512
  #site: 'webroot/site' #vhost root folder. relative or absolute path
  #site: '/site' # after chroot
  public: '<%this.srcPath%>/webroot/public'
  # publc root foler. relative to base or '/..' for absolute path
  #public: '/public' # after chroot
  #user: '<%this.srcPath%>/webroot/user' # need to set to relative path to webroot
  #store: '<%this.srcPath%>/webroot/fstore'
server:
  root: '<%this.srcPath%>/webroot' # base will be changed if this is set.
  #this is relative to orignal base too.
  noch: true
  #uid: 502
  #gid: 502
  stdout: '<%this.logPath%>/std.log'
  stderr: '<%this.logPath%>/err.log'
  port: <%this.server.port%>
  host: '<%this.server.host%>' #added lan access
  #host: '127.0.0.1'
  pid:  '<%this.logPath%>/cmate.pid'
  title: 'cmate'
proxy_header: 'x-real-ip'
log:
  console: true
  path: '<%this.logPath%>'
  mongodb:
    db:'chome'
    error: 'log_error'
    notfound: 'log_notfound'
  buffer:
    size:10
    duration:1000 #ms
  verbose:1
  sformat: ':date2 :remote-addr :method :host:url :start-time :user-agent :referrer'
  format: ':date2 :remote-addr :method :host:url :start-time :status :content-type :content-length :response-time'
  requests:
    limit: 10
    interval: 5000 # ms
    timeout: 100000 #ms developer 5s, product 100s
    level: 'warn'
multipart:
  uploadDir: '/tmp'
  keepExtensions: true
  multiples: false
production:
  error_message: "Internal Error. Please contact administrator. <EMAIL>"
lib:
  middleware:'<%this.srcPath%>/middleware'
  vendor:'<%this.srcPath%>/vendor'
themes: '<%this.srcPath%>/themes'
themesAutoReload: true
migration:'<%this.srcPath%>/built/migrate'
#apps: ['<%this.srcPath%>/appsTest'] # order is significently
apps: ['<%this.srcPath%>/built/model','<%this.srcPath%>/built/apps_common','<%this.srcPath%>/built/apps'] # order is significently
#exclude_apps: ['WebRM']
headerfile: 'header.coffee'
# limitAccessRate:
#   AccessPerSessionLimit: 110
#   SessionPerIPThreshold: 10
#   AvgAccessPerSessionIP: 2
app_config:
  azure:
    subscriptionKey: ""
    endpoint: "https://api.cognitive.microsofttranslator.com"
    region: "eastus"
  deepseek:
    key: ""
    endpoint: "https://api.deepseek.com/chat/completions"
  deepL:
    key:""
    endpoint: "https://api.deepl.com/v2/translate"
  openAI:
    key:""
    endpoint: "https://api.openai.com/v1/chat/completions"
    orgID:""
    projectID:""
  gemini:
    key:""
  claude:
    key:""
    endpoint: "https://api.anthropic.com/v1/messages"
  skipSendLeadToCrm: true
  defaultEmailFromName: 'RealMaster'
  mockMail:
    verbose: 3
    mock: false
  unitTest: true
  noSitemap: 1
  useSearchEngine: 'ES'
  projectFubEmail: '<EMAIL>'
  followUpBoss:
    specificEmail: '<EMAIL>'
    token:'xxfka_02gWVHLlA1VvNq1AR2X82ZuhXeFjtWLmjB'
    xSystem: 'realmasterWeb'
    xSystemKey: 'xxbade25031e36fc00f93db96e9fbc404c'
  elastic:
    host: '<%this.elasticSearch.host%>'
    user: '<%this.elasticSearch.user%>'
    password: '<%this.elasticSearch.password%>'
    cert: '<%this.elasticSearch.cert%>'
    verbose: 2
  importNotify:
    defaultTo: ['<EMAIL>']
    bcreTo: ['<EMAIL>']
  alertEmail: '<%this.alertEmail%>'
  alertSMS: '<%this.alertSMS%>'
  defaultEmail: '<%this.defaultSenderEmail%>'
  defaultRTEmail: '<%this.defaultRTEmail%>'
  trebManualImport:
    # relayDomain:'http://d9.realmaster.com'
    skipManualImportAuth:true
  propTagFilePath: '<%this.srcPath%>'
  wpBackEndDomain:'https://d6w.realmaster.cn/'
  verbose: 1
  shareHost: 'http://<%this.shareHost%>'
  kafka:
    clientId: 'batch-server'
    brokers: ['localhost:9092']
    verbose: 3
    consumerGroupId: 'batch-server'
    topic:
      condosToGetSqft: 'req.sqft.condos.local'
      sqftFromCondosCa: 'res.sqft.condosCa.local'
  useMysql:false
  usePostgresql:false
  sqldbs:
    listingPostsql:
      verbose: 2
      dbNm:'listingPostsql'
      tp:'pgsql'
      host: '<%this.pgsql.host%>'
      user: '<%this.pgsql.user%>'
      max: '<%this.pgsql.max%>'
      idleTimeoutMillis: '<%this.pgsql.idleTimeoutMillis%>'
      connectionTimeoutMillis: '<%this.pgsql.connectionTimeoutMillis%>'
      database: '<%this.pgsql.database%>'
      password: '<%this.pgsql.password%>'
      port: '<%this.pgsql.port%>'
  #   vow:
  #     type:'mysql'
  #     database: '<%this.mysql.db%>'
  #     user: '<%this.mysql.user%>'
  #     password: '<%this.mysql.pass%>'
  #     host: '<%this.mysql.host%>'
  #     insecureAuth:true
  #     port:'<%this.mysql.port%>'
  s3config:
    region:  'us-west-2' #'Oregon' #
    accessKeyId: '********************' #'********************'
    secretAccessKey: '5auap7yre6y1GaMegfq9cYCSq9uEDOnkCzeXjeUH' #'xcw9aw1rzbujrzImJLr/MpUPZyFREZuUsdkXTvOu'
    bucket: '<%this.s3.bucket%>'
  user_file:
    wwwbase: 'fs_t.realmaster.cn'
    folder: 'G'
    protocol: 'http'
    disableUpload: false
  publicImgFilePath: '<%this.srcPath%>/webroot/public/img' #'<%this.srcPath%>/webroot/public/img'
  publicImgErrorFile: '<%this.srcPath%>/s3uploadError.log'
  imgServerUlAddr: 'https://fu_t.realmaster.cn'
  imgServerDlAddr: 'https://f.realmaster.cn'
  media:
    wwwbase: 'http://realmaster.com'
    path: '<%this.srcPath%>/webroot/public'
  template:
    path: '<%this.srcPath%>/shareImgTemplate'
  emailTemplate:
    path: '<%this.srcPath%>/emailTemplate'
  eziIPs: ['']#['************','************'] #['***********','***********']
  isharingIPs: ['**************','***************']
  wechat: 'RealMaster'
  wechatAppId: 'wxf43abc63fd6c1b19'
  wechatSecret: 'fcd6d3800bb2906bacbee676685a0de4'
  wechatDomain: 'realmaster.cn'
  wechatWeb:
    AppID: 'wxd1989971aaef81e7'
    AppSecret: '0e3783110749989da5c93b170d04609a'
  wechatApp:
    AppID: 'wxcf33ce325754da25'
    AppSecret: '69a59252b9f587356b70706d4080d2ae'
  iShare:
    secret: 'ojF7llUbqMJTHxQLr75a6IrPtsnR6DyI'
    wwwbase: 'http://www.isharing.co/api/realmaster'
  share:
    secret: '547dc488a8d7d5828d34437872064a9b'
  wwwDomain: '<%this.wwwDomain%>'
  openexchangeratesKey: '<%this.openexchange%>' #'********************************' # <EMAIL>
  openexchangeratesInterval: 48
  wbcid: '39141494639361'
  wbcsec: 'xMPUNwIZFJrvTK08t0ak'
  geetestId: '9407943ac70054a2f029b6bd3dafbd27' #'32eb89e91d23af79b864ddea2a4a5361'
  geetestKey: '438cc575fff662a7564e29945ccb75b9' #'5d02237baf1cefb6b2b0b091a88a90d4'
  recaptchaSiteKey: '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI' #'6LfSgw4UAAAAAAel6FMchUgPJoO3vv_C4BUG1lDV'
  recaptchaSecretKey: '6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe' #'6LfSgw4UAAAAANsKMeOTHcxXHs3HXmbFQZZAwY1j'
  stripePubKey: 'pk_test_xuuOG1SkPt8l46NikwVbp39m'
  stripeSecret: 'sk_test_0cMa8JlEYGFI51Pv3Yn9cHdR'
  wordpressHost: 'http://**************:8080'
  wordpressJWT:'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************.hcrjbJnmZe6F9VjNiwL6aFPLcOp1sAIClR3r4UTbrWM'
  google:
    geocoderServer:'AIzaSyDfGNii_C0VMC6ku7bWCmeDOcd0RIzzfyI' #geocoder only, TODO:need restrict ip
    browser:'AIzaSyByOYTeTI9tQdhMb_V65fOp94ORIbGi4tA'
    # browser:'AIzaSyCcnrOBfqHK-eOMGcY8tTyEe5mNc7S_WeU' #for commute:direction, place,js.TODO: change commute to mapbox,wecard
    iosAnalytics:'AIzaSyBCqhaFdE2PJWB1pPSdBy4rkRwn8TtbGGg' #restrict to app
    ios:'AIzaSyAsj6IN_2_1hPr5f_niGK3sUvViIX7omqg' #map and autocomplete etc,testkey,
    iosTest:'AIzaSyDg3M9-IWfpswsto_nXAIX9ESHt987-SvY' #restrict ios bundle, TODO:@rain, test if working
    andriodAnalytics:'AIzaSyB3j8f64si2jzbsAxCAL5FAmb64GJS-2A8' # android only
    andriod:'AIzaSyBOslrM1NF12A_SX_SnhFXKoBxmfdeD7Gc' #map and autocomplete etc
    oldNative:'AIzaSyClYqxKKg2WdhFE9UDKA9ekFYHDIMLfdLA' #place(ios,android,sdk),map, geocoding,direction
    housemax:'AIzaSyC8WWwQzKqHPLTY5QHH82vSMM6egsJesmU'
  mapbox:
    web: 'pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A'
    app: 'pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A'
    housemax: 'pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A'
    address: 'pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A'
  here:'gI6hBaOR4eBPRKgiNE4qIUUDY6UZ-ZlAlZxZt2o5LZE',
  mapquest: '********************************'
mail:
  smtp:
    # NOTE: no longer used, not useable
    service: "Gmail"
    auth:
      user: '<EMAIL>'
      pass: 'dummy'
      # user:"<EMAIL>"
      # pass:"RealMasterCOM***"
# TODO: use new local mail engine
geoCoderService:
  token: 'aUIh0DMLQY'
  url: 'http://d1w.realmaster.cn/geocodingService'
  useRemoteService: true
  allowedIPs: [] # if it's empty array or undefined, ignore ip restriction
rmMail:
  defaultEmail: '<EMAIL>'
  url: 'https://ml1.realmaster.cc/send'
mailEngineList:[
  {
    engine: 'SES'
    options:
      accessKeyId: '********************'
      secretAccessKey: 'NqYJDdVhIMdvVqlJnmBSIBsbHKCEQLlkw91/v4rA'
      region: 'us-east-2'
      email: '<EMAIL>'
      url: 'http://9*************:8088/ses.php1'
  }
  {
    engine: 'rmMail'
    options:
      defaultEmail: '<EMAIL>'
      url: 'https://ml1.realmaster.cc/send'
  }
]
ses:
  accessKeyId: '********************'
  secretAccessKey: 'Uogp6eMqmbIhxB43mqCYPJYBT9x3vnQZwrawvz6W'
  region: 'us-east-1'
  defaultEmail: '<EMAIL>'
  url:'http://*************:8088/ses.php'
sendGrid:
  apiKey: '*********************************************************************'
  fromEmail: '<EMAIL>'
mailEngine: '<%this.mailEngine%>'
mailEngine2:'<%this.mailEngine2%>'
twilio:
  sid: '**********************************'
  authToken: '5ccb19e204f6cfa14dd333c1db9649e8'
wx_config:
  RealMaster:
    appid: 'wxf43abc63fd6c1b19'
    secret: 'fcd6d3800bb2906bacbee676685a0de4'
apn_config:
#  cert: '<%this.cfgPath%>/../keys/aps_production.pem'
#  key: '<%this.cfgPath%>/../keys/key.pem'
#  token:
#    key: "<%this.cfgPath%>/../keys/AuthKey_R8CP2B87H9.p8"
#    keyId: "R8CP2B87H9"
#    teamId: "3ZJZ7RQ3Q9"
  production: false
  passphrase: null
  topic: "com.realmaster"
gcm_config:
  key: 'AIzaSyDoqAESaLRAnXrfo4WR5iALPFU80D0uguw'
fcm_config:
  key: 'AAAAUBisLlk:APA91bHaz_PgjFcpxoIwuyTqikRqEmkprUuAyMu0BIrJYEdl3lU2M9tITF6783KZvQgqoJvlnVcERlJ5PMVtG_cBt2fQz5ivRvhxXL-DeSzTzqCmfkH_QqiDVkl29LYF6KC3hseB7Hh6'
socket:
  path: '/socket'
p58:
  protocol: 'http'
  host: 'autoinfo.gm.58.com' #'autoinfo.58v5.cn'
developer_mode: <%this.devMode%> #for product
chinaMode: <%this.chinaMode%>
satelliteMode: <%this.satelliteMode%>
masterMode: <%this.masterMode%>
ofe: false
profiler:
  freq: 60000
  file: '<%this.logPath%>/profile.log'
limitAccessRate:
  AccessPerSessionLimit: 9900
  SessionPerIPThreshold: 9100
  AvgAccessPerSessionIP: 920
preDefColls:
  mailLog:
    dbName:'chome'
    collName:'mail_log'
    options:
      timeseries:
        timeField:'timestamp'
        metaField:'metadata'
      expireAfterSeconds:30*24*3600
  emailWhitelist:
    dbName:'chome'
    collName:'email_whitelist'
  emailMXFailed:
    dbName:'chome'
    collName:'email_mx_failed'
  uuid:
    dbName:'chome'
    collName:'user_uuid'
preDefBatchColls:
  mailLog:
    dbName:'rni'
    collName:'mail_log'
    options:
      timeseries:
        timeField:'timestamp'
        metaField:'metadata'
      expireAfterSeconds:30*24*3600