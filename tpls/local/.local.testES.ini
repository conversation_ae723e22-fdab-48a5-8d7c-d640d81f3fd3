[contact]
trustedAssignEmail = '<EMAIL>'
alertSMS = '01234556'
alertEmail = '<EMAIL>'
defaultReciveEmail =''
projectFubEmail = '<EMAIL>'

[importNotify.bcreTo]
0 = "<EMAIL>"

[importNotify.defaultTo]
0 = "<EMAIL>"

[dbs]
verbose = 1

[dbs.chome]
uri="***************************************************************************************************************************************************************************"


[dbs.rni]
uri="****************************************************************************************************************************************************************************"


[dbs.tmp]
uri="**********************************************************************************************************************************************************************************"


[dbs.vow]
uri="******************************************************************************************************************************************************************************"


[server]
host = "0.0.0.0"
port = 8_098

[serverBase]
srcPath = "../realmaster-appweb/src"

[session.redis]
host = "************"

[elastic]
cert = "/mnt/mac/opt/homebrew/Cellar/elasticsearch-full/7.17.4/libexec/elastic-stack-ca.p12"
host = "https://************:9200"

[mailEngine]
mailEngine = "mockmail"

[mailEngine.gmail]
defaultEmail = ""

[mailEngine.gmail.auth]
pass = "dummy"
user = "<EMAIL>"

[mailEngine.mockMail]
mock = false
verbose = 3

[mailEngine.sendmail]
defaultEmail = ""

[mailEngine.sesH]
defaultEmail = ""

[mailEngine.sesL]
defaultEmail = ""

[user_file]
wwwbase = "fs_t.realmaster.c"

[propertiesBase]
useSearchEngine = "ES"

[limitAccessRate]
AccessPerSessionLimit = 9_900
AvgAccessPerSessionIP = 920
SessionPerIPThreshold = 9100

[serverBase]
appHostUrl=''
canonicalHost=''
srcPath=''
verbose = 1
wwwDomain = "www.test:8080"

[static]
verbose = 2
port = 8098

[share]
host = "http://www.test:8080"
hostNameCn = "realexpert.cn"
secret = "547dc488a8d7d5828d34437872064a9b"

[azure]
subscriptionKey = "e678a9f6c8764711849e0f17504aa696"