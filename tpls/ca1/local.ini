[apn]
cert = "${configPath}/keys/rain20251031cert.pem"
key = "${configPath}/keys/rain20251031key.pem"
production = true

[appleOauth]
authKeyPath = "${configPath}/keys/appleAuthKey.p8"
client_id = "com.realmaster.web"
client_id_app = "com.realmaster"
key_id = "5XJFAC59B8"
redirect_uri = "/oauth/apple"
scope = "name email"
team_id = "8A2DLASB37"

[apps]
folders = ["${srcPath}/appImgServer","${srcPath}/model","${srcPath}/microService","${srcPath}/apps_common","${srcPath}/apps"]

[azure]
subscriptionKey = "e678a9f6c8764711849e0f17504aa696"

[claude]
key = "************************************************************************************************************"

[contact]
alertEmail = ["<EMAIL>", "<EMAIL>"]
alertSMS = "6472223733,6476337771"
defaultReciveEmail = "<EMAIL>"
devEmails = "<EMAIL>"
projectFubEmail = "<EMAIL>"
trustedAssignEmail = "<EMAIL>"

[dbs]
verbose = 1

[dbs.chome]
uri ="*****************************************************************************************************************************************************************************************************************************************************************"

[dbs.rni]
uri = "*********************************************************************************************************************************************************************************************************************************************"

[dbs.tmp]
#uri = "*****************************************************************************************************************************************************************************************"
uri = "*********************************************************************************************************************************************************************************************************************************************"

[dbs.vow]
uri = "********************************************************************************************************************************************************************************************************************************************************************"

[deepL]
key = "c8311202-a251-97d9-b15d-3795ac88e820"

[deepseek]
key = "***********************************"

[elastic]
cert = "/home/<USER>/rmconfig/keys/http_ca.crt"
host = "https://localhost:9200"
password = "feFJzkfixEr367gVVoVx"
user = "elastic"
verbose = 1

[facebookOauth]
apiSecret = "339ec1a8538ee2889dd0e8606fc7cb0a"
appId = "357776481094717"
redirect = "/oauth/facebook"

[fileBase]
imgServerDlAddr = "https://f.realmaster.cn"
imgServerUlAddr = "https://fu.realmaster.cn" # 'http://file.test:8091'#'http://fu.t.realmaster.cn'
moveFile = "copy"
publicImgErrorFile = "/opt/rmfu/s3uploadError.log"
publicImgFilePath = ["/opt/rmfu/f_realmaster_cn/","/mnt/ca6m0/rmfu/f_realmaster_cn/","/mnt/ca7m0/rmfu/f_realmaster_cn/"]
#publicImgFilePath = ["/opt/rmfu/f_realmaster_cn/","/mnt/ca7m0/rmfu/f_realmaster_cn/"]

[followUpBoss]
specificEmail = "<EMAIL>"
token = "fka_02gWVHLlA1VvNq1AR2X82ZuhXeFjtWLmjB"
xSystem = "realmasterWeb"
xSystemKey = "bade25031e36fc00f93db96e9fbc404c"

[geetest]
id = "9407943ac70054a2f029b6bd3dafbd27"
key = "438cc575fff662a7564e29945ccb75b9"

[gemini]
key = "AIzaSyBHoyzQTkMF_Aps9ykYZ00BRHdtfGc_TRk"

[geoCoderService]
geoip = true
token = "aUIh0DMLQY"
url = "https://www.realmaster.com/geocodingService"
useRemoteService = false
allowedIPs = ["***************","***************"]
# if it's empty array or undefined, ignore ip restriction, TODO: allow avion/4salebc/...

[google]
andriod = "AIzaSyBOslrM1NF12A_SX_SnhFXKoBxmfdeD7Gc" # map and autocomplete etc
andriodAnalytics = "AIzaSyB3j8f64si2jzbsAxCAL5FAmb64GJS-2A8" # android only
browser = "AIzaSyCcnrOBfqHK-eOMGcY8tTyEe5mNc7S_WeU" # for commute:direction, place,js.TODO: change commute to mapbox,wecard
geocoderServer = "AIzaSyDfGNii_C0VMC6ku7bWCmeDOcd0RIzzfyI" # geocoder only, TODO:need restrict ip
housemax = "AIzaSyC8WWwQzKqHPLTY5QHH82vSMM6egsJesmU"
ios = "AIzaSyAsj6IN_2_1hPr5f_niGK3sUvViIX7omqg" # map and autocomplete etc,testkey,
iosAnalytics = "AIzaSyBCqhaFdE2PJWB1pPSdBy4rkRwn8TtbGGg" # restrict to app
oldNative = "AIzaSyClYqxKKg2WdhFE9UDKA9ekFYHDIMLfdLA" # place(ios,android,sdk),map, geocoding,direction
serverAutocomplete = "AIzaSyBkc-yNjTXMkxkvnXAXrfaAcV7GaOBFFxs" # server autocomplete key(in new version)
x_iosTest = "AIzaSyDg3M9-IWfpswsto_nXAIX9ESHt987-SvY" # restrict ios bundle, @rain, not work for ios bundle

[googleOauth]
clientId = "344011320921-vh4gmos4t6rej56k2oa3brharpio1nfn.apps.googleusercontent.com"
clientSecret = "9IE0XrIOOpA5ZmN0YWzyZXcM"
redirect = "/oauth/google"

[here]
key = "gI6hBaOR4eBPRKgiNE4qIUUDY6UZ-ZlAlZxZt2o5LZE"

[imageStore]
dnld_dir = "${trebBaseDir}/dnld/"
treb_image_dir = ["/mnt/ca6m0/mlsimgs/treb/mls"]
crea_image_dir = ["/mnt/ca6m0/mlsimgs/crea/ddf/img"]
oreb_image_dir = ["/mnt/ca6m0/mlsimgs/oreb/mls"]
creb_image_dir = ['/mnt/ca6m0/mlsimgs/creb/mls/' ,'/mnt/ca7m0/mlsimgs/creb/mls/']
reso_treb_image_dir = ["/mnt/ca6m0/imgs/MLS/TRB","/mnt/ca7m0/imgs/MLS/TRB"]
reso_car_image_dir = ["/mnt/ca6m0/imgs/MLS/CAR","/mnt/ca7m0/imgs/MLS/CAR"]
reso_crea_image_dir = ["/mnt/ca6m0/imgs/MLS/DDF","/mnt/ca7m0/imgs/MLS/DDF"]
reso_bcre_image_dir = ["/mnt/ca6m0/imgs/MLS/BRE","/mnt/ca7m0/imgs/MLS/BRE"]
reso_edm_image_dir = ["/mnt/ca6m0/imgs/MLS/EDM","/mnt/ca7m0/imgs/MLS/EDM"]

[creb]
user = 'RYULISOVOW'
pass = 'V024!$Aug'
photo_parallel = 10

[oreb]
user = 'AVIRvow'
pass = 'SW38pn44vow123!'
photo_parallel = 10

[importNotify.bcreTo]
0 = "<EMAIL>"

[importNotify.defaultTo]
0 = "<EMAIL>"

[kafka.main]
clientId = "ca1m-server"
ignoreThisServer = true
verbose = 1
brokers = ["ca1:9192","ca2:9192","ca3:9192"]


[limitAccessRate]
AccessPerSessionLimit = 350
AvgAccessPerSessionIP = 3
SessionPerIPThreshold = 350

[log]
path = "/home/<USER>/rmprodlogs"

[mailEngine.sesL]
accessKeyId = "********************"
secretAccessKey = "Uogp6eMqmbIhxB43mqCYPJYBT9x3vnQZwrawvz6W"
region = 'us-east-1'
defaultEmail = '<EMAIL>'

[mailEngine.sesH]
accessKeyId = "********************"
secretAccessKey = "NqYJDdVhIMdvVqlJnmBSIBsbHKCEQLlkw91/v4rA"
region = 'us-east-2'
defaultEmail = '<EMAIL>'

[mailEngineList.0]
accessKeyId = "********************"
secretAccessKey = "NqYJDdVhIMdvVqlJnmBSIBsbHKCEQLlkw91/v4rA"
region = 'us-east-2'
defaultEmail = '<EMAIL>'

[mailEngineList.1]
email = "<EMAIL>"
url = "https://ml1.realmaster.cc/send"

[mapbox]
address = "pk.eyJ1Ijoicm1zdXBwb3J0IiwiYSI6ImNrNXd0aTJqNzFjYTczam1jaGtxeGwzM3gifQ.nHvto-jVwvxc7xpTX1y7hA"
app = "pk.eyJ1IjoiZnJlZHhpYW5nIiwiYSI6ImNrNXdzZHVnZTE4bjUzZWswaDZocjFycXEifQ.aWq9qeqBoY2cOHkSLyMQKA"
housemax = "pk.eyJ1IjoiaG1heC1yZWFsbSIsImEiOiJjazY2cWdsaW4xb2N1M21vZmxidnZuNW43In0.mbrCkawOdMM--mnBnbIU8Q"
web = "pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czh6ZnExZ3NuM2pvZzB0b3dwY2xuIn0.1-ZrniVbO5fM_UzOe7Vo3Q"

[mapquest]
key = "********************************"

[moderation]
accessKeyId = "LTAI4FkKgRRk6D13Q323GoPx"
accessKeySecret = "******************************"
check = true
hostname = "green.cn-shanghai.aliyuncs.com"
bypass = ["politics","flood","meaningless","porn"]

[openAI]
key = "********************************************************************************************************************************************************************"
orgID = "org-XknsRihTPUr9OozG2H5byyOQ"
projectID = "proj_yPZHSJr6CWsj39U4jBvHfpdv"

[openExchange]
interval = 4
key = "98a8fbb72c1d423187674962078910d9" # '5bae32e4fbb24a02b6e650acc7e71159' # <EMAIL>

[openweathermap]
key = "********************************"

[pyfpimg.imageSource]
local_path_cda = "/mnt/md0/mlsimgs/crea/ddf/img/"
local_path_creb = "/mnt/md0/mlsimgs/creb/mls/"
local_path_orb = "/mnt/md0/mlsimgs/oreb/mls/"
local_path_oreb = "/mnt/md0/mlsimgs/oreb/mls/"
local_path_trb = "/mnt/md0/mlsimgs/treb/mls/"
url_host = "img.realmaster.com"

[pyfpimg.kafka.request]
_test_topic_init = "test-fpimg-request-init"
_test_topic_routine = "test-fpimg-request-routine"
auto_offset_reset = "earliest"
bootstrap_servers = "ca1:9192,ca2:9192,ca3:9192"
group_id = "ca3-consumer"
timeout = 60
topic_init = "fpimg-request-init"
topic_routine = "fpimg-request-routine"
worker_num = 3

[pyfpimg.kafka.response]
_test_topic_name = "test-fpimg-response"
bootstrap_servers = "ca1:9192,ca2:9192,ca3:9192"
topic_name = "fpimg-response"

[pyfpimg.mongo]
collection = "floorplan_classification"
host = "ca6"
name = "rni"
password = "RniPass"
port = 27_037
tlsCAFile = "/etc/mongo/ca.crt"
user = "rni"

[recapcha]
secretKey = "6LeMsw8UAAAAAGRLxWFVflvVXu4g53B-NZt4M6Iv"
siteKey = "6LeMsw8UAAAAAGKIonO4ImluXmOxwXKrT9tnl0PO"

[s3config]
accessKeyId = "********************" # '********************'
bucket = "f.realmaster.com"
region = "us-west-2" # 'Oregon'
secretAccessKey = "5auap7yre6y1GaMegfq9cYCSq9uEDOnkCzeXjeUH" # 'xcw9aw1rzbujrzImJLr/MpUPZyFREZuUsdkXTvOu'

[server]
host = "127.0.0.1"
noch = true
port = 8_085

[serverBase]
appHostUrl = "https://realmaster.com"
canonicalHost = "https://www.realmaster.com"
chinaMode = false
creaBaseDir = "/mnt/md0/mlsimgs/crea/ddf/"
createRNIIndex = true
debugThreshhold = 1
developer_mode = false
masterMode = true
satelliteMode = false
skipSendLeadToCrm = false
srcPath = "/opt/rmappweb/src"
trebBaseDir = "/mnt/md0/mlsimgs/treb/"
wwwDomain = "www.realmaster.cn"
verbose = 1
srcGoAppPath = "/opt/go/goapp/src"
srcGoBasePath = "/opt/gorepo"

[session.redis]
host = "127.0.0.1"

[settings.appweb.match]
hostname = "(app\\.)?realmaster\\.(com|cn)"

[settings.shareWeb.match]
hostname = "https://www.realmaster.com"

[settings.www.match]
hostname = "www\\.realmaster\\.(com|cn)"

[share]
host = "https://www.realmaster.cn"
hostNameCn = "realmaster.cn"
secret = "547dc488a8d7d5828d34437872064a9b"

[stripe]
pubKey = "pk_live_QYqyuEDiIXBy6R39TBbzmAin"
secret = "********************************"

[trebManualImport]
relayDomain = "http://c1.realmaster.com"
relayImport = false
skipAuth = false

[twilio]
authToken = "5ccb19e204f6cfa14dd333c1db9649e8"
sid = "**********************************"

[user_file]
disableUpload = false
folder = "P"
protocol = "https"
wwwbase = "f.realmaster.com"

[wechat]
AESKey = "MJelXnB2cNt4FomrM5mXIFO1vfGkRlqgEr683ifUKNE"
appId = "wx486c08aa3a05089d"
appName = "RealMaster"
domain = "realmaster.cn"
secret = "2b37e59a16594a8faebaa509a0da9956"
token = "d5e3f10d4f02930061eba8e771b1" # 微信消息推送接口校验

[wechatApp]
AppID = "wxcf33ce325754da25"
AppSecret = "69a59252b9f587356b70706d4080d2ae"
domain = 'https://realmaster.com/'

[wechatWeb]
AppID = "wx486c08aa3a05089d"
AppSecret = "2b37e59a16594a8faebaa509a0da9956"

[profiler]
file = "/home/<USER>/rmprodlogs/profile.log"

[bcre]
pass = "Hudr9ruWlbRaVop"
user = "RETSFREDXIANG"

[car]
token = "160a9de555855f4995e0fe77eac0ce42"

[crea]
pass = "WDbAEdaafc7CMJjnqjjWFNdh"
user = "BfHsE68ZfJtWrbeZXvPBpBxg"

[rae]
token = "f60627287e6931ec46453694089c5ccc"

[retsPhoto.ywa]
pass = "H9p3$63"
user = "D20ywa"

[retsPhoto.frx]
pass = "D2$w123"
user = "D23frx"

[treb.evow]
pass = "A7$e162"
user = "EV23frx"

[treb.idx]
pass = "D2$w123"
user = "D23frx"


[equifax]
  clientId='jn2CulzGCKC3GBze93C2cXeWHxueHsQR'
  clientSecret='l6Vaz1X1BR9GSRy5'
  db='vow'
  col='equifax'
[equifax.api]
  token = 'https://api.equifax.ca/v2/oauth/token'
  report =  'https://api.equifax.ca/inquiry/1.0/sts'
  scope = 'https://api.equifax.ca/inquiry/1.0/sts'
[equifax.customer]
  customerCode = 'R346'
  customerNumber = '467RE01185'
  securityCode = '75'
  customerId = 'for billing get from Clients'
  customerReferenceNumber = '2495'

[trebReso.evow]
photo_parallel = 10
token = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ2ZW5kb3IvdHJyZWIvNjQ0OCIsImF1ZCI6IkFtcFVzZXJzUHJkIiwicm9sZXMiOlsiQW1wVmVuZG9yIl0sImlzcyI6InByb2QuYW1wcmUuY2EiLCJleHAiOjI1MzQwMjMwMDc5OSwiaWF0IjoxNzMzMTY1ODIwLCJzdWJqZWN0VHlwZSI6InZlbmRvciIsInN1YmplY3RLZXkiOiI2NDQ4IiwianRpIjoiNzJmZmIyYzhhOTJkN2QyOSIsImN1c3RvbWVyTmFtZSI6InRycmViIn0.Rmlu2tdM8D0yb1Id3pu_H2-WWXmuY55dZa576jJZbec"

[trebReso.idx]
photo_parallel = 10
token = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ2ZW5kb3IvdHJyZWIvNjQ0OCIsImF1ZCI6IkFtcFVzZXJzUHJkIiwicm9sZXMiOlsiQW1wVmVuZG9yIl0sImlzcyI6InByb2QuYW1wcmUuY2EiLCJleHAiOjI1MzQwMjMwMDc5OSwiaWF0IjoxNzMzMTY1ODQ3LCJzdWJqZWN0VHlwZSI6InZlbmRvciIsInN1YmplY3RLZXkiOiI2NDQ4IiwianRpIjoiYWMzYWQxM2MyMzljOTJjNiIsImN1c3RvbWVyTmFtZSI6InRycmViIn0.j7f4cW8f4NLL9OHNR9cUvaeevICtr66SvDksu1gXCAQ"

[bcreReso]
token = "592c83b063a1a5e4d3c33e9029a23c93"

[secretkey]
secret = 'ukRsy7o4MLZnHdoE50XM5KSZHQgyw653'
iv = 'b9bcb2d39e09ca12' # '7B5A581741C83'

[grok]
endpoint = "https://api.x.ai/v1/chat/completions"
key = "************************************************************************************"

[creaReso]
pass = "F22neS0XpsAF3HpaYTU8oNiH"
user = "e6ze2S6Y85KJGwN1gCv1VNki"

[golog]
dir = "/home/<USER>/rmgoprodlogs"
level = "info"
#level = "debug"
#verbose = "verbose.log"
#info = "info.log"
#error = "error.log"
format = "text"
standard_output = true

[oauthProvider]
secret =  "b9lzajneH2MiTrUeRzaSyCzCBgaRfib29lzajna"

[[oauthProvider.clients]]
clientId = "report_rentals"
clientSecret = "x7QmpLkeW9NuGrVoTkbDyEzXAqdPwtm39QmpLkeb"
redirectUri =  "https://report.rentals/oauth/callback"

[azureGeocoder]
primaryKey = "DwQofnQuoeZL4lQRqe4XPlPJi2cOqROLIKAqZlLnoDU1p3Fcg8CQJQQJ99BFACYeBjFet9xLAAAgAZMP2QlU"
secondaryKey = "C02a5bMiqGMu1mBerKqHcfLQz6TWPjlr4sWZbVAaTVsIH2bYMMjTJQQJ99BFACYeBjFet9xLAAAgAZMP3lMg"