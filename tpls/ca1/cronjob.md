

# New config:
NEXT                        LEFT                LAST                        PASSED              UNIT                                  ACTIVATES
Fri 2025-07-11 11:45:00 EDT 1min 16s left       Fri 2025-07-11 11:40:28 EDT 3min 14s ago        <EMAIL>                 <EMAIL>
Fri 2025-07-11 11:45:00 EDT 1min 16s left       Fri 2025-07-11 11:40:28 EDT 3min 14s ago        batch@reDownloadRni_day3.timer        batch@reDownloadRni_day3.service
Fri 2025-07-11 11:45:00 EDT 1min 16s left       Fri 2025-07-11 11:40:28 EDT 3min 14s ago        batch@reDownloadRni_day7.timer        batch@reDownloadRni_day7.service
Fri 2025-07-11 11:45:00 EDT 1min 16s left       Fri 2025-07-11 11:30:28 EDT 13min ago           <EMAIL>                  <EMAIL>
Fri 2025-07-11 11:45:00 EDT 1min 16s left       Fri 2025-07-11 11:15:28 EDT 28min ago           <EMAIL>                    <EMAIL>
Fri 2025-07-11 17:05:00 EDT 5h 21min left       Thu 2025-07-10 17:05:28 EDT 18h ago             <EMAIL>                  <EMAIL>
Fri 2025-07-11 18:01:00 EDT 6h left             Thu 2025-07-10 18:01:28 EDT 17h ago             <EMAIL>               <EMAIL>
Fri 2025-07-11 21:00:00 EDT 9h left             Fri 2025-07-11 09:00:28 EDT 2h 43min ago        <EMAIL>               <EMAIL>
Sat 2025-07-12 01:01:00 EDT 13h left            Sat 2025-07-05 01:01:01 EDT 6 days ago          <EMAIL>                 <EMAIL>
Sat 2025-07-12 01:30:00 EDT 13h left            Fri 2025-07-11 01:30:28 EDT 10h ago             <EMAIL>              <EMAIL>
Sat 2025-07-12 02:00:00 EDT 14h left            Fri 2025-07-11 02:00:28 EDT 9h ago              <EMAIL> <EMAIL>
Sat 2025-07-12 02:00:00 EDT 14h left            Sat 2025-07-05 02:00:28 EDT 6 days ago          <EMAIL>                  <EMAIL>
Sat 2025-07-12 02:30:00 EDT 14h left            Fri 2025-07-11 02:30:28 EDT 9h ago              <EMAIL>       <EMAIL>
Sat 2025-07-12 02:30:00 EDT 14h left            Fri 2025-07-11 02:30:28 EDT 9h ago              <EMAIL>    <EMAIL>
Sat 2025-07-12 02:30:00 EDT 14h left            Fri 2025-07-11 02:30:28 EDT 9h ago              <EMAIL>                <EMAIL>
Sat 2025-07-12 03:00:00 EDT 15h left            Sat 2025-07-05 03:00:28 EDT 6 days ago          <EMAIL>                  <EMAIL>
Sat 2025-07-12 03:01:00 EDT 15h left            Fri 2025-07-11 03:01:28 EDT 8h ago              <EMAIL>            <EMAIL>
Sat 2025-07-12 03:30:00 EDT 15h left            Fri 2025-07-11 03:30:28 EDT 8h ago              <EMAIL>                <EMAIL>
Sat 2025-07-12 03:45:00 EDT 16h left            Fri 2025-07-11 03:45:28 EDT 7h ago              <EMAIL>        <EMAIL>
Sat 2025-07-12 07:01:00 EDT 19h left            Fri 2025-07-11 07:01:02 EDT 4h 42min ago        <EMAIL>                  <EMAIL>
Mon 2025-07-14 01:01:00 EDT 2 days left         Mon 2025-07-07 01:01:01 EDT 4 days ago          <EMAIL>              <EMAIL>
Mon 2025-07-14 03:00:00 EDT 2 days left         Mon 2025-07-07 03:00:28 EDT 4 days ago          <EMAIL>         <EMAIL>
Wed 2025-07-16 17:07:00 EDT 5 days left         Wed 2025-07-09 17:07:28 EDT 1 day 18h ago       <EMAIL>              <EMAIL>
Thu 2025-07-17 18:30:00 EDT 6 days left         Thu 2025-07-10 18:30:28 EDT 17h ago             <EMAIL>           <EMAIL>
Fri 2025-08-01 01:00:00 EDT 2 weeks 6 days left Tue 2025-07-01 01:00:28 EDT 1 week 3 days ago   <EMAIL> <EMAIL>
Sat 2025-08-02 01:00:00 EDT 3 weeks 0 days left Wed 2025-07-02 01:00:28 EDT 1 week 2 days ago   <EMAIL>      <EMAIL>
Thu 2025-08-07 17:05:00 EDT 3 weeks 6 days left Mon 2025-07-07 17:05:28 EDT 3 days ago          <EMAIL>                 <EMAIL>
Fri 2025-08-08 18:01:00 EDT 4 weeks 0 days left Tue 2025-07-08 18:01:28 EDT 2 days ago          <EMAIL>    <EMAIL>

```bash
# 15 mins
./start.sh -t batch -n sendMail -cmd "lib/batchBase.coffee batch/sendMail.coffee preload-model production" -cron '*-*-* *:00,15,30,45:00'
./start.sh -t batch -n mongoTest -cmd "lib/batchBase.coffee batch/test/mongoTest.coffee" -cron '*:0/5'

# 30 mins
./start.sh -t batch -n wechat -cmd "lib/batchBase.coffee batch/wechat.coffee" -cron '*-*-* *:15,45:00'

# hourly
# sh start.sh -t batch -n dtaDownload -cmd "lib/batchBase.coffee mlsImport/dtaDownload.coffee -f hourly -s 1" -cron '*-*-* *:10:00'

# daily
sh start.sh -t batch -n statsLastW -cmd "lib/batchBase.coffee stats/prop_mw.coffee LastW" -cron '*-*-* 02:30:00'
sh start.sh -t batch -n statsLastM -cmd "lib/batchBase.coffee stats/prop_mw.coffee LastM" -cron '*-*-* 03:30:00'
./start.sh -t batch -n statsPropDaily -cmd "lib/batchBase.coffee stats/prop_daily.coffee" -cron '*-*-* 03:01:00'
./start.sh -t batch -n checkRniTrebPropAndExpire -cmd "lib/batchBase.coffee batch/prop/checkRniTrebPropAndExpire.coffee" -cron '*-*-* 02:00:00'

./start.sh -t batch -n dailyNotify -cmd "lib/batchBase.coffee batch/dailyNotifyV2.coffee prod preload-model " -cron '*-*-* 09,21:00:00'
./start.sh -t batch -n wepageClearCounter -cmd "lib/batchBase.coffee batch/wepageClearCounter.coffee" -cron '*-*-* 03:45:00'
./start.sh -t batch -n generateSchRmMerged -cmd "lib/batchBase.coffee batch/school/generateSchRmMerged.coffee" -cron '*-*-* 02:30:00'

# projectrcmd
./start.sh -t batch -n edmDaily -cmd "lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw  assignment exlisting daily production" -cron '*-*-01..06,08..31 17:05:00'
./start.sh -t batch -n edmDaily7 -cmd "lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw  statM rmlog assignment exlisting daily production" -cron '*-*-7 17:05:00'
./start.sh -t batch -n edmDailySub -cmd "lib/batchBase.coffee batch/edmNotify.coffee preload-model property production" -cron '*-*-* 18:01:00'

# findschool
./start.sh -t batch -n findschoolv5 -cmd "lib/batchBase.coffee batch/findschool/findschoolv5.coffee -f daily -s 1" -cron '*-*-* 01:30:00'
# dump form input to Tao
sh start.sh -t batch -n formdump -cmd "lib/batchBase.coffee batch/form/formdump.coffee -e '<EMAIL>' -e '<EMAIL>' --frequency daily -s 1" -cron '*-*-* 07:01:00'

# findschool update bnds
./start.sh -t batch -n processSchoolBndChange -cmd "lib/batchBase.coffee batch/prop/processSchoolBndChange.coffee preload-model -f" -cron '*-*-* 02:30:00'

# weekly
# project
./start.sh -t batch -n edmWeekly -cmd "lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw forum stat rmlog noviewed assignment exlisting weekly production" -cron 'Sat, 01:01:00'
./start.sh -t batch -n edmExpWeekly -cmd "lib/batchBase.coffee batch/edmExpNotify.coffee preload-model 10 production" -cron 'Mon, 01:01:00'
./start.sh -t batch -n laWeekly -cmd "lib/batchBase.coffee batch/user/getAgentWithListings.coffee" -cron 'Sat, 02:00:00'
./start.sh -t batch -n reImportProp -cmd "lib/batchBase.coffee batch/prop/reImportProp.coffee preload-model -d 2024-01-01" -cron 'Wed, 17:07:00'
./start.sh -t batch -n i18nStat -cmd "lib/batchBase.coffee batch/i18n/i18nStat.coffee" -cron 'Sat, 03:00:00'

./start.sh -t batch -n reDownloadRni_day3 -cmd "lib/batchBase.coffee batch/prop/reDownloadRni.coffee -a 3" -cron '*:0/5'
./start.sh -t batch -n reDownloadRni_day7 -cmd "lib/batchBase.coffee batch/prop/reDownloadRni.coffee -a 7" -cron '*:0/5'

./start.sh -t batch -n weeklyTokenReport -cmd "lib/batchBase.coffee batch/form/weeklyTokenReport.coffee -e <EMAIL> -e <EMAIL> -e <EMAIL>" -cron 'Mon, 03:00:00'
./start.sh -t batch -n reImportBuzProp -cmd "lib/batchBase.coffee batch/prop/reImportProp.coffee preload-model -b" -cron 'Thu, 21:00:00'

# monthly
./start.sh -t batch -n agntRealChatState-monthly -cmd "lib/batchBase.coffee batch/user/getAgentRealChatStat.coffee 30" -cron '*-*-01 01:00:00'
sh start.sh -t batch -n addBuiltYear-monthly -cmd "lib/batchBase.coffee batch/prop/addBuiltYear.coffee" -cron '*-*-02 01:00:00'
sh start.sh -t batch -n exportInRealGroupStats -cmd "lib/batchBase.coffee batch/export/inRealGroupStats.coffee -m `date -d "28 days ago" +'%Y-%m'` -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL>" -cron '*-*-02 04:01:00'


```