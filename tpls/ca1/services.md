# following services are running on ca1
```bash
# watchers for ES/prop/push/trans/price
./start.sh -t batch -n watchPropAndUpdateElasticSearch -cmd "lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee routine force"

./start.sh -t batch -n watchAndImportToProperties -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t creaReso -t reso -t creb -t car -t edm -t bcreReso -h 1 routine force"

./start.sh -t batch -n watchPropAndCompareSoldPrice -cmd "lib/batchBase.coffee batch/prop/watchPropAndCompareSoldPrice.coffee preload-model routine"

./start.sh -t batch -n watchPropAndTranslate -cmd "lib/batchBase.coffee batch/prop/watchPropAndTranslate.coffee preload-model"

./start.sh -t batch -n watchPropAndPushNotify -cmd "lib/batchBase.coffee batch/prop/watchPropAndPushNotify.coffee preload-model routine"
```



## Download workers
```bash
./start.sh -t batch -n trebResoDownload -cmd 'lib/batchBase.coffee mlsImport/trrebReso/trebResoDownload.coffee evow force'

./start.sh -t batch -n bcreResoDownload -cmd 'lib/batchBase.coffee mlsImport/bcreReso/bcreResoDownload.coffee routine'

./start.sh -t batch -n creaResoDownload -cmd 'lib/batchBase.coffee mlsImport/creaReso/creaResoDownload.coffee routine'

./start.sh -t batch -n raeDownload -cmd 'lib/batchBase.coffee mlsImport/edmonton/raeDownload.coffee -t f60627287e6931ec46453694089c5ccc'

./start.sh -t batch -n carDownload -cmd 'lib/batchBase.coffee mlsImport/cornerstone/carDownload.coffee'

./start.sh -t batch -n crebDownload -cmd "lib/batchBase.coffee mlsImport/creb/crebDownload.coffee force"

```


# cronjobs
see cronjob.md



# dirs 
log_dir: /home/<USER>/rmprodlogs
src_dir: /opt/rmappweb/src
cfg_dir: /home/<USER>/rmprodconfig

# helpful commands, see more at rmconfig/readme.md
## 如果更新了local.ini 必须stop service，用restart无法加载新config
```bash
zsh
wd cfg
wd src
wd logs_new

systemctl --user list-units --type=service --no-pager
list-timers

systemctl --user status appweb
systemctl --user restart appweb.service #会导致server interuption
systemctl --user status batch@crebDownload

systemctl --user stop batch@watchAndImportToProperties
systemctl --user stop batch@

# 去掉红色的failed service
services=(
  "batch@watchES_init.service"
  "<EMAIL>"
)
for service in "${services[@]}"; do
  echo "Remove: $service"
  systemctl --user stop "$service"
  systemctl --user disable "$service"
  systemctl --user reset-failed "$service"
  echo "RM: ~/.config/systemd/user/$service"
  rm -f ~/.config/systemd/user/"$service"
done

```


# NOTE: 
Restart=no should be default for batch and cronjobs


# one time
./start.sh -t batch -n floorPlanWaterMark -cmd "lib/batchBase.coffee batch/condos/floorPlanWaterMark.coffee -m local -p /opt/rmfu/f_realmaster_cn/ -b /mnt/ca3/floorplan/raw_a -w /mnt/ca3/floorplan/FPA  -r true dryrun"