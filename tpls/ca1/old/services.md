# Old cronjob using old config

# 15 mins
0,15,30,45 * * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/sendMail.coffee preload-model production >> /home/<USER>/rmcfg/logs/sendMail.log 2>&1

# 30 mins
15,45 * * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/wechat.coffee >> /home/<USER>/rmcfg/logs/wechat.log 2>&1

# hourly
10 0-23 * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee mlsImport/dtaDownload.coffee -f hourly -s 1 >> /home/<USER>/rmcfg/logs/dta.log 2>&1



# daily
30 2 * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee stats/prop_mw.coffee LastW >> /home/<USER>/rmcfg/logs/statsLastW.log 2>&1
30 3 * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee stats/prop_mw.coffee LastM >> /home/<USER>/rmcfg/logs/statsLastM.log 2>&1
1 3 * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee stats/prop_daily.coffee >> /home/<USER>/rmcfg/logs/statsPropDaily.log 2>&1

0 9,21 * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/dailyNotifyV2.coffee prod preload-model >> /home/<USER>/rmcfg/logs/dailyNotify.log 2>&1
45 3 * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/wepageClearCounter.coffee >> /home/<USER>/rmcfg/logs/wepageClearCounter.log 2>&1

5 17 1-6,8-31 * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw projectrcmd assignment exlisting daily production >> /home/<USER>/rmcfg/logs/edmDaily.log 2>&1
5 17 7 * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw projectrcmd statM rmlog assignment exlisting daily production >> /home/<USER>/rmcfg/logs/edmDaily.log 2>&1
1 18 * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model property production >> /home/<USER>/rmcfg/logs/edmDailySub.log 2>&1

# findschool
30 1 * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/findschool/findschoolv4.coffee -f daily -s 1 >> /home/<USER>/rmcfg/logs/findschoolv4.log 2>&1
# dump form input to Tao
1 7 * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/form/formdump.coffee -e '<EMAIL>' -e '<EMAIL>' --frequency daily -s 1 >> /home/<USER>/rmcfg/logs/formdump.log 2>&1
# statUname & addtag
10 1 * * * /home/<USER>/rmcfg/built/statUname.sh -t calendar >> /home/<USER>/rmcfg/logs/statUnameCalendar.log 2>&1
# findschool update bnds
30 2 * * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/prop/processSchoolBndChange.coffee preload-model -f >> /home/<USER>/rmcfg/logs/processSchoolBndChange.log 2>&1

# weekly
1 1 * * 6 /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw project forum stat rmlog noviewed assignment exlisting weekly production >> /home/<USER>/rmcfg/logs/edmWeekly.log 2>&1
1 1 * * 1 /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/edmExpNotify.coffee preload-model 10 production >> /home/<USER>/rmcfg/logs/edmExpWeekly.log 2>&1
0 2 * * 6 /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/user/getAgentWithListings.coffee >> /home/<USER>/rmcfg/logs/laWeekly.log 2>&1
07 17 * * 3 /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/prop/reImportProp.coffee  >> /home/<USER>/rmcfg/logs/reImportProp.log 2>&1 &
#10 3 * * 1 /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/removeDDFImage.coffee 365 /mnt/md0/mlsimgs/crea/ddf/img >> /home/<USER>/rmcfg/logs/removeDDFImage.log 2>&1
#10 3 * * 2 /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/removeTREBImage.coffee 365 /mnt/md0/mlsimgs/treb/mls >> /home/<USER>/rmcfg/logs/removeTrebImage.log 2>&1

# monthly
0 1 1 * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/user/getAgentRealChatStat.coffee 30 >> /home/<USER>/rmcfg/logs/agntRealChatState-monthly.log 2>&1
0 1 2 * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/prop/addBuiltYear.coffee >> /home/<USER>/rmcfg/logs/addBuiltYear-monthly.log 2>&1
# inreal monthly stat report
#1 0 3 * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/export/inRealGroupStats.coffee -m `date -d "1 month ago" +'%Y-%m'` -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL> > /home/<USER>/rmcfg/logs/exportInRealGroupStats.log 2>&1 &
# 1 4 2 * * /home/<USER>/rmcfg/sendInRealStatus.sh > /home/<USER>/rmcfg/logs/exportInRealGroupStats.log 2>&1 &
1 4 2 * * /home/<USER>/rmprodconfig/start.sh lib/batchBase.coffee batch/export/inRealGroupStats.coffee -m `date -d "1 month ago" +'%Y-%m'` -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL> > /home/<USER>/rmcfg/logs/exportInRealGroupStats.log 2>&1 &

# daily backup
#10 3 * * * /home/<USER>/bin/wpBackup.sh >> /home/<USER>/logs/wpBackup.log
#30 3 * * * /home/<USER>/bin/dbBackup.sh >> /home/<USER>/logs/dbBackup.log