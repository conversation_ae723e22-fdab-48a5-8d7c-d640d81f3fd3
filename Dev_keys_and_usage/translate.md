# 翻译使用AI+接口
see： [add_translation_deepseek]
20240813_add_translation_deepseek.md

| AI Name | link | key | doc | price | Price batch | 测试 | 翻译结果 |
|---|---|---|---|---|---|---|---|
| Google Gemini | https://ai.google.dev/ | AIzaSyBHoyzQTkMF_Aps9ykYZ00BRHdtfGc_TRk | https://ai.google.dev/gemini-api/docs/quickstart?hl=zh-cn&lang=node | Gemini 1.0 Pro $0.50 / 1M input tokens $1.50 / 1M output tokens Gemini 1.5 Pro $3.5 for <= 128K input tokens $7 for > 128K input tokens $10.50 for <= 128K output tokens $21 for > 128K output tokens Gemini 1.5 Flash $0.075 for <= 128K input tokens $0.15 for > 128K input tokens $0.30 for <= 128K output tokens $0.60 for > 128K output tokens |  | *1条remarks速度测试2364ms | 位于一个热门街区的转角处，拥有三个朝向的两个合法地块，并可通过巷道进入！ 这块 65.72 英尺 x 120 英尺的房间四块地是难得的机会。同一业主还拥有隔壁的 780 Victoria 地块，如果一起购买，将可以开发一个 11,829 平方英尺的地点。所有看房均需预约，地点无标志。 |
| GPT | https://openai.com/index/gpt-4/ | *****************************************************************************************************************************************************************  org-XknsRihTPUr9OozG2H5byyOQ proj_yPZHSJr6CWsj39U4jBvHfpdv | https://platform.openai.com/docs/api-reference/introduction | gpt-3.5-turbo-0125 $0.50 / 1M input tokens $1.50 / 1M output tokens gpt-4o-mini $0.150 / 1M input tokens $0.600 / 1M output tokens gpt-4o $5.00 / 1M input tokens $15.00 / 1M output tokens |  | *1条remarks速度测试3306ms | 两块合法土地位于一个转角处，具有巷道通道和三个朝向！位于温哥华最热门的社区之一。这块65.72’ x 120’的4房用地是一个难得的机会。相同的业主拥有位于780 Victoria的邻近土地，若一起购买将形成11,829平方英尺的开发用地。所有看房均需预约，现场无告示牌。 |
| Anthropic Claude3 | https://www.anthropic.com/ | ************************************************************************************************************ | https://docs.anthropic.com/en/api/messages | Claude 3.5 Sonnet input $3/MTok, output $15/MTok Claude 3 Opus  input $15/MTok, output $75/MTok Claude 3 Haiku input $0.25/MTok, output $1.25/MTok |  | *1条remarks速度测试1439ms | 在温哥华最热门的社区之一,有TWO LEGAL LOTS位于CORNER,拥有LANE ACCESS并三面敞开!这块65.72'x120'的4号地块是难得一遇的机会。 该地块的同一所有者还拥有隔壁的780 Victoria地块,两者合并后可形成11,829平方英尺的开发用地。 所有预约参观,现场不设标识。 |
| deepseek | https://www.deepseek.com/ | *********************************** | https://platform.deepseek.com/api-docs/zh-cn/ | 1-2元/百万token |  | 100条remarks，Average Response Time: 8232.84 ms. Standard Deviation of Response Time: 2549.30 ms token使用约3.6万，约0.07元。 *1条remarks速度测试6791ms | 位于温哥华热门社区的拐角处，拥有巷道通达及三面采光，两块法定地块！这块面积为65.72英尺乘以120英尺的4号地块实属难得机遇。同一业主还拥有隔壁780 Victoria地块，若一并购入，将形成总面积达11,829平方英尺的开发用地。所有看房均需预约，现场不设指示牌。 |
| Azure translate | https://azure.microsoft.com/en-us/products/ai-services/ai-translator | e678a9f6c8764711849e0f17504aa696 | https://learn.microsoft.com/zh-cn/azure/ai-services/translator/ | $13.854 per million characters |  | 每月10万条remarks，200CAD *1条remarks速度测试148ms | 两个合法地块位于拐角处，该位置有车道通道，有三个曝光点！位于温哥华最热门的街区之一。这 65.72' x 120' room-4 lot 是一个难得的机会。同一业主在780 Victoria拥有隔壁的地块，一起购买将使该地点的开发面积为11， 829平方英尺。所有放映仅限预约，没有在位置上签名。 |
|  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |

old config:
```
app_config:
  azure:
    subscriptionKey: "e678a9f6c8764711849e0f17504aa696"
    endpoint: "https://api.cognitive.microsofttranslator.com"
    region: "eastus"
  deepseek:
    key: "***********************************"
    endpoint: "https://api.deepseek.com/chat/completions"
  deepL:
    key:"c8311202-a251-97d9-b15d-3795ac88e820"
    endpoint: "https://api.deepl.com/v2/translate"
  openAI:
    key:"org-XknsRihTPUr9OozG2H5byyOQ"
    endpoint: "https://api.openai.com/v1/chat/completions"
    orgID:"org-XknsRihTPUr9OozG2H5byyOQ"
    projectID:"proj_yPZHSJr6CWsj39U4jBvHfpdv"
  gemini:
    key:"AIzaSyBHoyzQTkMF_Aps9ykYZ00BRHdtfGc_TRk"
  claude:
    key:"************************************************************************************************************"
    endpoint: "https://api.anthropic.com/v1/messages"
```
new config:
```
_translate.ini
[azure]
subscriptionKey = "e678a9f6c8764711849e0f17504aa696"
endpoint = "https://api.cognitive.microsofttranslator.com"
region = "eastus"

[deepseek]
key = "***********************************"
endpoint = "https://api.deepseek.com/chat/completions"

[deepL]
key = "c8311202-a251-97d9-b15d-3795ac88e820"
endpoint = "https://api.deepl.com/v2/translate"

[openAI]
key = "********************************************************************************************************************************************************************"
endpoint = "https://api.openai.com/v1/chat/completions"
orgID = "org-XknsRihTPUr9OozG2H5byyOQ"
projectID = "proj_yPZHSJr6CWsj39U4jBvHfpdv"

[gemini]
key = "AIzaSyBHoyzQTkMF_Aps9ykYZ00BRHdtfGc_TRk"

[claude]
key = "************************************************************************************************************"
endpoint = "https://api.anthropic.com/v1/messages"

```

# !!!Key is wrong!!!
use ********************************************************************************************************************************************************************
