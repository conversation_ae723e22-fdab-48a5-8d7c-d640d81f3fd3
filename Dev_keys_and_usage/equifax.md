# weather keys
在app/showing里经纪会消耗token展示user credit repoer
see: [equifax api](https://developer.equifax.com/help-support/user-guide)



# 问题
测试版app和正式版key， domain/ip不同无法通用，导致api无法加载

# 解决
测试版要在测试服测试，不然会被ip ban， 但是错误信息为403


# 管理员需要的操作
1. 在equifax 后台测试版升级为正式版
2. 监测key使用情况

# key
App Name:	      test-9d5dddf9-cfde-4ae8-997d-79b5e0292956-avion-sts
API Approved:	  Test-Equifax-Report-STS
ClientId:       O5OqoO8s8RfyjKyyGF1wdVVWrtPsTJXu
Client Secret:  4v8UtPc5Cj88yZjf
UAT member Number: 999RE00254
Security Code: 99

Customer security Code: R346
Security code: 
Customer reference number: opt changed 999
Customer id/number: opt, canbe changed
# API
customer numer has its own

# prod
1 promote in dev portal
2 production cust number secrity code