# google keys
在react-native-maps 需要用到google的key展示地图
see: [react-native-maps](https://github.com/react-native-maps/react-native-maps/blob/master/docs/installation.md)
[google](https://developers.google.com/maps/documentation/android-sdk/get-api-key?hl=zh-cn)

软件包名称
com.example
必须提供软件包名称

SHA-1 证书指纹
12:34:56:78:90:AB:CD:EF:12:34:56:78:90:AB:CD:EF:AA:BB:CC:DD
必须提供指纹

# 问题
1. 测试版app和正式版appSHA-1 证书指纹不一样，所以key无法通用，导致地图无法加载。
2. 注册key需要信用卡信息，需要外国地址，google会charge和返还退款测试卡是否可用

# 解决
测试版暂时使用无限制的key，正式版会用prod key


# 管理员需要的操作
1 注册测试用key，不限制条件
2 注册正式key，限制指纹证书
3 监测测试key使用情况

# key
test:  AIzaSyDgi2mHkM6kdJhpxc3ubEbvid9w7GQ4-fY
 
prod: AIzaSyBOslrM1NF12A_SX_SnhFXKoBxmfdeD7Gc
com.realmaster
fingerPrint: 
